{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/types/order.ts"], "sourcesContent": ["// 訂單相關類型定義\n\n// 訂單狀態枚舉\nexport enum OrderStatus {\n  PENDING = 'PENDING',           // 待付款\n  PAID = 'PAID',                 // 已付款\n  PROCESSING = 'PROCESSING',     // 處理中\n  SHIPPED = 'SHIPPED',           // 已發貨\n  DELIVERED = 'DELIVERED',       // 已送達\n  CANCELLED = 'CANCELLED',       // 已取消\n  REFUNDED = 'REFUNDED'          // 已退款\n}\n\n// 支付方式枚舉\nexport enum PaymentMethod {\n  CREDIT_CARD = 'CREDIT_CARD',   // 信用卡\n  DEBIT_CARD = 'DEBIT_CARD',     // 借記卡\n  PAYPAL = 'PAYPAL',             // PayPal\n  ALIPAY = 'ALIPAY',             // 支付寶\n  WECHAT_PAY = 'WECHAT_PAY',     // 微信支付\n  CASH_ON_DELIVERY = 'CASH_ON_DELIVERY' // 貨到付款\n}\n\n// 收貨地址接口\nexport interface Address {\n  id?: number;\n  userId?: number;\n  recipientName: string;         // 收件人姓名\n  phone: string;                 // 聯繫電話\n  province: string;              // 省份\n  city: string;                  // 城市\n  district: string;              // 區/縣\n  detailAddress: string;         // 詳細地址\n  postalCode?: string;           // 郵政編碼\n  isDefault: boolean;            // 是否為默認地址\n  createdAt?: string;\n  updatedAt?: string;\n}\n\n// 訂單商品項接口\nexport interface OrderItem {\n  id?: number;\n  orderId?: number;\n  productId: number;\n  productName: string;\n  productImage: string;\n  price: number;                 // 下單時的價格\n  quantity: number;\n  subtotal: number;              // 小計 (price * quantity)\n}\n\n// 訂單接口\nexport interface Order {\n  id?: number;\n  userId?: number;\n  orderNumber: string;           // 訂單號\n  status: OrderStatus;\n  items: OrderItem[];\n  \n  // 價格信息\n  subtotal: number;              // 商品小計\n  shippingFee: number;           // 運費\n  discount: number;              // 折扣金額\n  totalAmount: number;           // 總金額\n  \n  // 收貨信息\n  shippingAddress: Address;\n  \n  // 支付信息\n  paymentMethod: PaymentMethod;\n  paymentStatus: 'PENDING' | 'COMPLETED' | 'FAILED';\n  paidAt?: string;\n  \n  // 時間戳\n  createdAt: string;\n  updatedAt: string;\n  \n  // 備註\n  notes?: string;\n}\n\n// 創建訂單請求接口\nexport interface CreateOrderRequest {\n  items: {\n    productId: number;\n    quantity: number;\n  }[];\n  shippingAddressId: number;\n  paymentMethod: PaymentMethod;\n  notes?: string;\n}\n\n// 發貨信息接口\nexport interface ShippingInfo {\n  id?: number;\n  orderId: number;\n  trackingNumber: string;\n  carrier: string;\n  shippingMethod: string;\n  shippedAt: string;\n  estimatedDelivery?: string;\n  notes?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 發貨請求接口\nexport interface ShippingRequest {\n  trackingNumber: string;\n  carrier: string;\n  shippingMethod: string;\n  notes?: string;\n  estimatedDays?: number;\n}\n\n// 常用快遞公司\nexport const CARRIERS = {\n  SF: '順豐速運',\n  YTO: '圓通速遞',\n  ZTO: '中通快遞',\n  STO: '申通快遞',\n  EMS: '中國郵政EMS',\n  JD: '京東物流'\n} as const;\n\n// 配送方式\nexport const SHIPPING_METHODS = {\n  STANDARD: '標準配送',\n  EXPRESS: '加急配送',\n  SAME_DAY: '當日達',\n  NEXT_DAY: '次日達'\n} as const;\n\n// 訂單統計接口\nexport interface OrderSummary {\n  totalOrders: number;\n  pendingOrders: number;\n  completedOrders: number;\n  totalSpent: number;\n}\n"], "names": [], "mappings": "AAAA,WAAW;AAEX,SAAS;;;;;;;AACF,IAAA,AAAK,qCAAA;;;;;;;0CAOqB,MAAM;WAP3B;;AAWL,IAAA,AAAK,uCAAA;;;;;;4DAM4B,OAAO;WANnC;;AAsGL,MAAM,WAAW;IACtB,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;AACN;AAGO,MAAM,mBAAmB;IAC9B,UAAU;IACV,SAAS;IACT,UAAU;IACV,UAAU;AACZ", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/store/orderStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { Order, OrderStatus, CreateOrderRequest, OrderItem, ShippingInfo, ShippingRequest } from '../types/order';\n\n// 訂單管理狀態接口\ninterface OrderState {\n  orders: Order[];\n  currentOrder: Order | null;\n  isLoading: boolean;\n  error: string | null;\n\n  // 發貨信息緩存\n  shippingInfos: Map<number, ShippingInfo>; // orderId -> ShippingInfo\n\n  // 操作方法\n  createOrder: (orderData: CreateOrderRequest) => Promise<Order>;\n  getOrderById: (id: number) => Order | null;\n  getUserOrders: (userId?: number) => Order[];\n  updateOrderStatus: (orderId: number, status: OrderStatus) => void;\n  cancelOrder: (orderId: number) => void;\n  setCurrentOrder: (order: Order | null) => void;\n  clearOrders: () => void;\n\n  // 發貨相關方法\n  shipOrder: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;\n  getOrderShipping: (orderId: number) => Promise<ShippingInfo | null>;\n  updateOrderShipping: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;\n  getOrdersReadyToShip: () => Order[];\n\n  // 統計方法\n  getOrdersByStatus: (status: OrderStatus) => Order[];\n  getTotalSpent: () => number;\n}\n\n// 生成訂單號\nconst generateOrderNumber = (): string => {\n  const timestamp = Date.now();\n  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n  return `ORD${timestamp}${random}`;\n};\n\n// 生成臨時ID（實際項目中應該由後端生成）\nlet tempOrderId = 1;\n\n// 創建訂單管理 store\nexport const useOrderStore = create<OrderState>()(\n  persist(\n    (set, get) => ({\n      orders: [],\n      currentOrder: null,\n      isLoading: false,\n      error: null,\n      shippingInfos: new Map(),\n\n      // 創建訂單\n      createOrder: async (orderData: CreateOrderRequest) => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          // 模擬API調用延遲\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          \n          // 這裡應該調用實際的API\n          // const response = await fetch('/api/orders', { method: 'POST', body: JSON.stringify(orderData) });\n          // const order = await response.json();\n          \n          // 模擬創建訂單\n          const now = new Date().toISOString();\n          const orderNumber = generateOrderNumber();\n          \n          // 計算訂單項（這裡需要從購物車或傳入的數據獲取商品信息）\n          const orderItems: OrderItem[] = orderData.items.map(item => ({\n            productId: item.productId,\n            productName: `商品 ${item.productId}`, // 實際應該從商品數據獲取\n            productImage: '/placeholder.jpg',\n            price: 99.99, // 實際應該從商品數據獲取\n            quantity: item.quantity,\n            subtotal: 99.99 * item.quantity\n          }));\n          \n          const subtotal = orderItems.reduce((sum, item) => sum + item.subtotal, 0);\n          const shippingFee = subtotal >= 99 ? 0 : 10;\n          const discount = 0;\n          const totalAmount = subtotal + shippingFee - discount;\n          \n          const newOrder: Order = {\n            id: tempOrderId++,\n            orderNumber,\n            status: OrderStatus.PENDING,\n            items: orderItems,\n            subtotal,\n            shippingFee,\n            discount,\n            totalAmount,\n            shippingAddress: {\n              id: orderData.shippingAddressId,\n              recipientName: '收件人',\n              phone: '13800138000',\n              province: '廣東省',\n              city: '深圳市',\n              district: '南山區',\n              detailAddress: '科技園南區',\n              isDefault: false\n            }, // 實際應該從地址數據獲取\n            paymentMethod: orderData.paymentMethod,\n            paymentStatus: 'PENDING',\n            createdAt: now,\n            updatedAt: now,\n            notes: orderData.notes\n          };\n          \n          const { orders } = get();\n          set({ \n            orders: [newOrder, ...orders],\n            currentOrder: newOrder,\n            isLoading: false \n          });\n          \n          return newOrder;\n        } catch (error) {\n          set({ \n            error: error instanceof Error ? error.message : '創建訂單失敗',\n            isLoading: false \n          });\n          throw error;\n        }\n      },\n\n      // 根據ID獲取訂單\n      getOrderById: (id: number) => {\n        const { orders } = get();\n        return orders.find(order => order.id === id) || null;\n      },\n\n      // 獲取用戶訂單\n      getUserOrders: (userId?: number) => {\n        const { orders } = get();\n        // 如果沒有傳入userId，返回所有訂單（模擬當前用戶的訂單）\n        return orders;\n      },\n\n      // 更新訂單狀態\n      updateOrderStatus: (orderId: number, status: OrderStatus) => {\n        const { orders } = get();\n        const updatedOrders = orders.map(order => {\n          if (order.id === orderId) {\n            const updatedOrder = {\n              ...order,\n              status,\n              updatedAt: new Date().toISOString()\n            };\n            \n            // 如果狀態變為已付款，更新支付狀態和時間\n            if (status === OrderStatus.PAID) {\n              updatedOrder.paymentStatus = 'COMPLETED';\n              updatedOrder.paidAt = new Date().toISOString();\n            }\n            \n            return updatedOrder;\n          }\n          return order;\n        });\n\n        set({ orders: updatedOrders });\n      },\n\n      // 取消訂單\n      cancelOrder: (orderId: number) => {\n        get().updateOrderStatus(orderId, OrderStatus.CANCELLED);\n      },\n\n      // 設置當前訂單\n      setCurrentOrder: (order: Order | null) => {\n        set({ currentOrder: order });\n      },\n\n      // 清空訂單\n      clearOrders: () => {\n        set({\n          orders: [],\n          currentOrder: null,\n          error: null\n        });\n      },\n\n      // 根據狀態獲取訂單\n      getOrdersByStatus: (status: OrderStatus) => {\n        const { orders } = get();\n        return orders.filter(order => order.status === status);\n      },\n\n      // 計算總消費\n      getTotalSpent: () => {\n        const { orders } = get();\n        return orders\n          .filter(order => order.status !== OrderStatus.CANCELLED)\n          .reduce((total, order) => total + order.totalAmount, 0);\n      },\n\n      // 發貨相關方法\n      shipOrder: async (orderId: number, shippingData: ShippingRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          // 模擬API調用\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // 實際應該調用後端API\n          // const response = await fetch(`/api/orders/${orderId}/ship`, {\n          //   method: 'POST',\n          //   headers: { 'Content-Type': 'application/json' },\n          //   body: JSON.stringify(shippingData)\n          // });\n          // const shippingInfo = await response.json();\n\n          // 模擬創建發貨信息\n          const now = new Date().toISOString();\n          const shippingInfo: ShippingInfo = {\n            id: Date.now(),\n            orderId,\n            trackingNumber: shippingData.trackingNumber,\n            carrier: shippingData.carrier,\n            shippingMethod: shippingData.shippingMethod,\n            shippedAt: now,\n            estimatedDelivery: shippingData.estimatedDays\n              ? new Date(Date.now() + shippingData.estimatedDays * 24 * 60 * 60 * 1000).toISOString()\n              : undefined,\n            notes: shippingData.notes,\n            createdAt: now,\n            updatedAt: now\n          };\n\n          // 更新發貨信息緩存\n          const { shippingInfos } = get();\n          const newShippingInfos = new Map(shippingInfos);\n          newShippingInfos.set(orderId, shippingInfo);\n\n          // 更新訂單狀態為已發貨\n          get().updateOrderStatus(orderId, OrderStatus.SHIPPED);\n\n          set({\n            shippingInfos: newShippingInfos,\n            isLoading: false\n          });\n\n          return shippingInfo;\n        } catch (error) {\n          set({\n            error: error instanceof Error ? error.message : '發貨失敗',\n            isLoading: false\n          });\n          throw error;\n        }\n      },\n\n      getOrderShipping: async (orderId: number) => {\n        const { shippingInfos } = get();\n\n        // 先檢查緩存\n        const cachedShipping = shippingInfos.get(orderId);\n        if (cachedShipping) {\n          return cachedShipping;\n        }\n\n        try {\n          // 實際應該調用後端API\n          // const response = await fetch(`/api/orders/${orderId}/shipping`);\n          // if (response.ok) {\n          //   const shippingInfo = await response.json();\n          //   // 更新緩存\n          //   const newShippingInfos = new Map(shippingInfos);\n          //   newShippingInfos.set(orderId, shippingInfo);\n          //   set({ shippingInfos: newShippingInfos });\n          //   return shippingInfo;\n          // }\n\n          return null;\n        } catch (error) {\n          console.error('獲取發貨信息失敗:', error);\n          return null;\n        }\n      },\n\n      updateOrderShipping: async (orderId: number, shippingData: ShippingRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          // 模擬API調用\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // 實際應該調用後端API\n          // const response = await fetch(`/api/orders/${orderId}/shipping`, {\n          //   method: 'PUT',\n          //   headers: { 'Content-Type': 'application/json' },\n          //   body: JSON.stringify(shippingData)\n          // });\n          // const shippingInfo = await response.json();\n\n          // 模擬更新發貨信息\n          const { shippingInfos } = get();\n          const existingShipping = shippingInfos.get(orderId);\n\n          if (!existingShipping) {\n            throw new Error('該訂單尚未發貨');\n          }\n\n          const updatedShipping: ShippingInfo = {\n            ...existingShipping,\n            trackingNumber: shippingData.trackingNumber,\n            carrier: shippingData.carrier,\n            shippingMethod: shippingData.shippingMethod,\n            notes: shippingData.notes,\n            estimatedDelivery: shippingData.estimatedDays\n              ? new Date(new Date(existingShipping.shippedAt).getTime() + shippingData.estimatedDays * 24 * 60 * 60 * 1000).toISOString()\n              : existingShipping.estimatedDelivery,\n            updatedAt: new Date().toISOString()\n          };\n\n          // 更新緩存\n          const newShippingInfos = new Map(shippingInfos);\n          newShippingInfos.set(orderId, updatedShipping);\n\n          set({\n            shippingInfos: newShippingInfos,\n            isLoading: false\n          });\n\n          return updatedShipping;\n        } catch (error) {\n          set({\n            error: error instanceof Error ? error.message : '更新發貨信息失敗',\n            isLoading: false\n          });\n          throw error;\n        }\n      },\n\n      getOrdersReadyToShip: () => {\n        const { orders } = get();\n        return orders.filter(order =>\n          order.status === OrderStatus.PAID || order.status === OrderStatus.PROCESSING\n        );\n      }\n    }),\n    {\n      name: 'order-storage',\n      version: 1,\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAgCA,QAAQ;AACR,MAAM,sBAAsB;IAC1B,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACvE,OAAO,CAAC,GAAG,EAAE,YAAY,QAAQ;AACnC;AAEA,uBAAuB;AACvB,IAAI,cAAc;AAGX,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,QAAQ,EAAE;QACV,cAAc;QACd,WAAW;QACX,OAAO;QACP,eAAe,IAAI;QAEnB,OAAO;QACP,aAAa,OAAO;YAClB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,YAAY;gBACZ,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,eAAe;gBACf,oGAAoG;gBACpG,uCAAuC;gBAEvC,SAAS;gBACT,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,cAAc;gBAEpB,8BAA8B;gBAC9B,MAAM,aAA0B,UAAU,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC3D,WAAW,KAAK,SAAS;wBACzB,aAAa,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;wBACnC,cAAc;wBACd,OAAO;wBACP,UAAU,KAAK,QAAQ;wBACvB,UAAU,QAAQ,KAAK,QAAQ;oBACjC,CAAC;gBAED,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;gBACvE,MAAM,cAAc,YAAY,KAAK,IAAI;gBACzC,MAAM,WAAW;gBACjB,MAAM,cAAc,WAAW,cAAc;gBAE7C,MAAM,WAAkB;oBACtB,IAAI;oBACJ;oBACA,QAAQ,wHAAA,CAAA,cAAW,CAAC,OAAO;oBAC3B,OAAO;oBACP;oBACA;oBACA;oBACA;oBACA,iBAAiB;wBACf,IAAI,UAAU,iBAAiB;wBAC/B,eAAe;wBACf,OAAO;wBACP,UAAU;wBACV,MAAM;wBACN,UAAU;wBACV,eAAe;wBACf,WAAW;oBACb;oBACA,eAAe,UAAU,aAAa;oBACtC,eAAe;oBACf,WAAW;oBACX,WAAW;oBACX,OAAO,UAAU,KAAK;gBACxB;gBAEA,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,IAAI;oBACF,QAAQ;wBAAC;2BAAa;qBAAO;oBAC7B,cAAc;oBACd,WAAW;gBACb;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,MAAM;YACR;QACF;QAEA,WAAW;QACX,cAAc,CAAC;YACb,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO;QAClD;QAEA,SAAS;QACT,eAAe,CAAC;YACd,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,iCAAiC;YACjC,OAAO;QACT;QAEA,SAAS;QACT,mBAAmB,CAAC,SAAiB;YACnC,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA;gBAC/B,IAAI,MAAM,EAAE,KAAK,SAAS;oBACxB,MAAM,eAAe;wBACnB,GAAG,KAAK;wBACR;wBACA,WAAW,IAAI,OAAO,WAAW;oBACnC;oBAEA,sBAAsB;oBACtB,IAAI,WAAW,wHAAA,CAAA,cAAW,CAAC,IAAI,EAAE;wBAC/B,aAAa,aAAa,GAAG;wBAC7B,aAAa,MAAM,GAAG,IAAI,OAAO,WAAW;oBAC9C;oBAEA,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,IAAI;gBAAE,QAAQ;YAAc;QAC9B;QAEA,OAAO;QACP,aAAa,CAAC;YACZ,MAAM,iBAAiB,CAAC,SAAS,wHAAA,CAAA,cAAW,CAAC,SAAS;QACxD;QAEA,SAAS;QACT,iBAAiB,CAAC;YAChB,IAAI;gBAAE,cAAc;YAAM;QAC5B;QAEA,OAAO;QACP,aAAa;YACX,IAAI;gBACF,QAAQ,EAAE;gBACV,cAAc;gBACd,OAAO;YACT;QACF;QAEA,WAAW;QACX,mBAAmB,CAAC;YAClB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QACjD;QAEA,QAAQ;QACR,eAAe;YACb,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,OACJ,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,wHAAA,CAAA,cAAW,CAAC,SAAS,EACtD,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,WAAW,EAAE;QACzD;QAEA,SAAS;QACT,WAAW,OAAO,SAAiB;YACjC,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,UAAU;gBACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,cAAc;gBACd,gEAAgE;gBAChE,oBAAoB;gBACpB,qDAAqD;gBACrD,uCAAuC;gBACvC,MAAM;gBACN,8CAA8C;gBAE9C,WAAW;gBACX,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,eAA6B;oBACjC,IAAI,KAAK,GAAG;oBACZ;oBACA,gBAAgB,aAAa,cAAc;oBAC3C,SAAS,aAAa,OAAO;oBAC7B,gBAAgB,aAAa,cAAc;oBAC3C,WAAW;oBACX,mBAAmB,aAAa,aAAa,GACzC,IAAI,KAAK,KAAK,GAAG,KAAK,aAAa,aAAa,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW,KACnF;oBACJ,OAAO,aAAa,KAAK;oBACzB,WAAW;oBACX,WAAW;gBACb;gBAEA,WAAW;gBACX,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,mBAAmB,IAAI,IAAI;gBACjC,iBAAiB,GAAG,CAAC,SAAS;gBAE9B,aAAa;gBACb,MAAM,iBAAiB,CAAC,SAAS,wHAAA,CAAA,cAAW,CAAC,OAAO;gBAEpD,IAAI;oBACF,eAAe;oBACf,WAAW;gBACb;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,MAAM;YACR;QACF;QAEA,kBAAkB,OAAO;YACvB,MAAM,EAAE,aAAa,EAAE,GAAG;YAE1B,QAAQ;YACR,MAAM,iBAAiB,cAAc,GAAG,CAAC;YACzC,IAAI,gBAAgB;gBAClB,OAAO;YACT;YAEA,IAAI;gBACF,cAAc;gBACd,mEAAmE;gBACnE,qBAAqB;gBACrB,gDAAgD;gBAChD,YAAY;gBACZ,qDAAqD;gBACrD,iDAAiD;gBACjD,8CAA8C;gBAC9C,yBAAyB;gBACzB,IAAI;gBAEJ,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,OAAO;YACT;QACF;QAEA,qBAAqB,OAAO,SAAiB;YAC3C,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,UAAU;gBACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,cAAc;gBACd,oEAAoE;gBACpE,mBAAmB;gBACnB,qDAAqD;gBACrD,uCAAuC;gBACvC,MAAM;gBACN,8CAA8C;gBAE9C,WAAW;gBACX,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,mBAAmB,cAAc,GAAG,CAAC;gBAE3C,IAAI,CAAC,kBAAkB;oBACrB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAgC;oBACpC,GAAG,gBAAgB;oBACnB,gBAAgB,aAAa,cAAc;oBAC3C,SAAS,aAAa,OAAO;oBAC7B,gBAAgB,aAAa,cAAc;oBAC3C,OAAO,aAAa,KAAK;oBACzB,mBAAmB,aAAa,aAAa,GACzC,IAAI,KAAK,IAAI,KAAK,iBAAiB,SAAS,EAAE,OAAO,KAAK,aAAa,aAAa,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW,KACvH,iBAAiB,iBAAiB;oBACtC,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAEA,OAAO;gBACP,MAAM,mBAAmB,IAAI,IAAI;gBACjC,iBAAiB,GAAG,CAAC,SAAS;gBAE9B,IAAI;oBACF,eAAe;oBACf,WAAW;gBACb;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,MAAM;YACR;QACF;QAEA,sBAAsB;YACpB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,MAAM,KAAK,wHAAA,CAAA,cAAW,CAAC,IAAI,IAAI,MAAM,MAAM,KAAK,wHAAA,CAAA,cAAW,CAAC,UAAU;QAEhF;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS;AACX", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Order/ShippingModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { XMarkIcon, TruckIcon } from '@heroicons/react/24/outline';\nimport { useOrderStore } from '../../store/orderStore';\nimport { ShippingRequest, CARRIERS, SHIPPING_METHODS } from '../../types/order';\nimport toast from 'react-hot-toast';\n\ninterface ShippingModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  orderId: number;\n  orderNumber: string;\n}\n\nexport default function ShippingModal({ isOpen, onClose, orderId, orderNumber }: ShippingModalProps) {\n  const { shipOrder, isLoading } = useOrderStore();\n  const [formData, setFormData] = useState<ShippingRequest>({\n    trackingNumber: '',\n    carrier: '',\n    shippingMethod: '',\n    notes: '',\n    estimatedDays: 3\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.trackingNumber.trim()) {\n      toast.error('請輸入運單號');\n      return;\n    }\n    \n    if (!formData.carrier) {\n      toast.error('請選擇快遞公司');\n      return;\n    }\n    \n    if (!formData.shippingMethod) {\n      toast.error('請選擇配送方式');\n      return;\n    }\n\n    try {\n      await shipOrder(orderId, formData);\n      toast.success('發貨成功！');\n      onClose();\n      // 重置表單\n      setFormData({\n        trackingNumber: '',\n        carrier: '',\n        shippingMethod: '',\n        notes: '',\n        estimatedDays: 3\n      });\n    } catch (error) {\n      toast.error('發貨失敗，請重試');\n    }\n  };\n\n  const handleInputChange = (field: keyof ShippingRequest, value: string | number) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\n      <div className=\"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0\">\n        {/* 背景遮罩 */}\n        <div \n          className=\"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75\"\n          onClick={onClose}\n        />\n\n        {/* 模態框 */}\n        <div className=\"inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg\">\n          {/* 標題 */}\n          <div className=\"flex items-center justify-between mb-6\">\n            <div className=\"flex items-center space-x-2\">\n              <TruckIcon className=\"w-6 h-6 text-blue-600\" />\n              <h3 className=\"text-lg font-semibold text-gray-900\">訂單發貨</h3>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <XMarkIcon className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* 訂單信息 */}\n          <div className=\"mb-6 p-3 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm text-gray-600\">訂單號</p>\n            <p className=\"font-medium text-gray-900\">{orderNumber}</p>\n          </div>\n\n          {/* 發貨表單 */}\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            {/* 運單號 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                運單號 <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"text\"\n                value={formData.trackingNumber}\n                onChange={(e) => handleInputChange('trackingNumber', e.target.value)}\n                placeholder=\"請輸入運單號\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              />\n            </div>\n\n            {/* 快遞公司 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                快遞公司 <span className=\"text-red-500\">*</span>\n              </label>\n              <select\n                value={formData.carrier}\n                onChange={(e) => handleInputChange('carrier', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              >\n                <option value=\"\">請選擇快遞公司</option>\n                {Object.entries(CARRIERS).map(([key, value]) => (\n                  <option key={key} value={value}>{value}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* 配送方式 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                配送方式 <span className=\"text-red-500\">*</span>\n              </label>\n              <select\n                value={formData.shippingMethod}\n                onChange={(e) => handleInputChange('shippingMethod', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                required\n              >\n                <option value=\"\">請選擇配送方式</option>\n                {Object.entries(SHIPPING_METHODS).map(([key, value]) => (\n                  <option key={key} value={value}>{value}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* 預計送達天數 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                預計送達天數\n              </label>\n              <input\n                type=\"number\"\n                min=\"1\"\n                max=\"30\"\n                value={formData.estimatedDays || ''}\n                onChange={(e) => handleInputChange('estimatedDays', parseInt(e.target.value) || 0)}\n                placeholder=\"預計送達天數\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n\n            {/* 備註 */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                備註\n              </label>\n              <textarea\n                value={formData.notes}\n                onChange={(e) => handleInputChange('notes', e.target.value)}\n                placeholder=\"發貨備註（可選）\"\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n\n            {/* 操作按鈕 */}\n            <div className=\"flex space-x-3 pt-4\">\n              <button\n                type=\"button\"\n                onClick={onClose}\n                className=\"flex-1 px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors\"\n                disabled={isLoading}\n              >\n                取消\n              </button>\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? '發貨中...' : '確認發貨'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAee,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAsB;;IACjG,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,gBAAgB;QAChB,SAAS;QACT,gBAAgB;QAChB,OAAO;QACP,eAAe;IACjB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,cAAc,CAAC,IAAI,IAAI;YACnC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,cAAc,EAAE;YAC5B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,UAAU,SAAS;YACzB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA,OAAO;YACP,YAAY;gBACV,gBAAgB;gBAChB,SAAS;gBACT,gBAAgB;gBAChB,OAAO;gBACP,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA8B;QACvD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;;;;;;;8CAEtD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CACrC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDAA+C;8DAC1D,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAErC,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,cAAc;4CAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACnE,aAAY;4CACZ,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAKZ,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDAA+C;8DACzD,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtC,6LAAC;4CACC,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;4CAC5D,WAAU;4CACV,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,OAAO,OAAO,CAAC,wHAAA,CAAA,WAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACzC,6LAAC;wDAAiB,OAAO;kEAAQ;uDAApB;;;;;;;;;;;;;;;;;8CAMnB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;;gDAA+C;8DACzD,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtC,6LAAC;4CACC,OAAO,SAAS,cAAc;4CAC9B,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACnE,WAAU;4CACV,QAAQ;;8DAER,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,OAAO,OAAO,CAAC,wHAAA,CAAA,mBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACjD,6LAAC;wDAAiB,OAAO;kEAAQ;uDAApB;;;;;;;;;;;;;;;;;8CAMnB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,KAAI;4CACJ,KAAI;4CACJ,OAAO,SAAS,aAAa,IAAI;4CACjC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CAChF,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAKd,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4CAC1D,aAAY;4CACZ,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAKd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;4CACV,UAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC;GA/LwB;;QACW,6HAAA,CAAA,gBAAa;;;KADxB", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/app/orders/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { EyeIcon, ClockIcon, CheckCircleIcon, TruckIcon } from '@heroicons/react/24/outline';\nimport { useOrderStore } from '../../store/orderStore';\nimport { OrderStatus } from '../../types/order';\nimport ShippingModal from '../../components/Order/ShippingModal';\n\nexport default function OrdersPage() {\n  const { orders, getUserOrders, getOrdersByStatus } = useOrderStore();\n  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | 'ALL'>('ALL');\n  const [shippingModal, setShippingModal] = useState<{\n    isOpen: boolean;\n    orderId?: number;\n    orderNumber?: string;\n  }>({ isOpen: false });\n\n  // 模拟管理员权限检查（实际应该从认证状态获取）\n  const isAdmin = true; // 这里应该从用户认证状态获取\n\n  // 检查订单是否可以发货\n  const canShipOrder = (status: OrderStatus) => {\n    return status === OrderStatus.PAID || status === OrderStatus.PROCESSING;\n  };\n\n  // 打开发货模态框\n  const openShippingModal = (orderId: number, orderNumber: string) => {\n    setShippingModal({\n      isOpen: true,\n      orderId,\n      orderNumber\n    });\n  };\n\n  // 关闭发货模态框\n  const closeShippingModal = () => {\n    setShippingModal({ isOpen: false });\n  };\n\n  // 獲取過濾後的訂單\n  const filteredOrders = selectedStatus === 'ALL' \n    ? getUserOrders() \n    : getOrdersByStatus(selectedStatus);\n\n  // 狀態過濾選項\n  const statusFilters = [\n    { key: 'ALL', label: '全部', count: orders.length },\n    { key: OrderStatus.PENDING, label: '待付款', count: getOrdersByStatus(OrderStatus.PENDING).length },\n    { key: OrderStatus.PAID, label: '已付款', count: getOrdersByStatus(OrderStatus.PAID).length },\n    { key: OrderStatus.SHIPPED, label: '已發貨', count: getOrdersByStatus(OrderStatus.SHIPPED).length },\n    { key: OrderStatus.DELIVERED, label: '已送達', count: getOrdersByStatus(OrderStatus.DELIVERED).length },\n    { key: OrderStatus.CANCELLED, label: '已取消', count: getOrdersByStatus(OrderStatus.CANCELLED).length },\n  ];\n\n  // 獲取訂單狀態信息\n  const getStatusInfo = (status: OrderStatus) => {\n    switch (status) {\n      case OrderStatus.PENDING:\n        return { text: '待付款', color: 'text-orange-600', bgColor: 'bg-orange-100', icon: ClockIcon };\n      case OrderStatus.PAID:\n        return { text: '已付款', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: CheckCircleIcon };\n      case OrderStatus.PROCESSING:\n        return { text: '處理中', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: ClockIcon };\n      case OrderStatus.SHIPPED:\n        return { text: '已發貨', color: 'text-purple-600', bgColor: 'bg-purple-100', icon: TruckIcon };\n      case OrderStatus.DELIVERED:\n        return { text: '已送達', color: 'text-green-600', bgColor: 'bg-green-100', icon: CheckCircleIcon };\n      case OrderStatus.CANCELLED:\n        return { text: '已取消', color: 'text-red-600', bgColor: 'bg-red-100', icon: ClockIcon };\n      default:\n        return { text: '未知', color: 'text-gray-600', bgColor: 'bg-gray-100', icon: ClockIcon };\n    }\n  };\n\n  // 處理圖片URL\n  const getImageUrl = (url: string, productName: string) => {\n    if (!url) return '/placeholder.jpg';\n    \n    if (url.startsWith('/images/')) {\n      return `https://via.placeholder.com/60x60/f0f0f0/666666?text=${encodeURIComponent(productName)}`;\n    }\n    \n    try {\n      new URL(url);\n      return url;\n    } catch {\n      return `https://via.placeholder.com/60x60/f0f0f0/666666?text=${encodeURIComponent(productName)}`;\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* 頁面標題 */}\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">我的訂單</h1>\n        <p className=\"text-gray-600 mt-1\">查看和管理您的所有訂單</p>\n      </div>\n\n      {/* 狀態過濾 */}\n      <div className=\"bg-white rounded-lg border border-gray-200 p-4 mb-6\">\n        <div className=\"flex flex-wrap gap-2\">\n          {statusFilters.map((filter) => (\n            <button\n              key={filter.key}\n              onClick={() => setSelectedStatus(filter.key as OrderStatus | 'ALL')}\n              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                selectedStatus === filter.key\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n              }`}\n            >\n              {filter.label}\n              {filter.count > 0 && (\n                <span className={`ml-1 ${\n                  selectedStatus === filter.key ? 'text-blue-200' : 'text-gray-500'\n                }`}>\n                  ({filter.count})\n                </span>\n              )}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* 訂單列表 */}\n      {filteredOrders.length === 0 ? (\n        <div className=\"bg-white rounded-lg border border-gray-200 p-12 text-center\">\n          <div className=\"text-gray-400 mb-4\">\n            <ClockIcon className=\"w-16 h-16 mx-auto\" />\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">暫無訂單</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {selectedStatus === 'ALL' ? '您還沒有任何訂單' : `暫無${statusFilters.find(f => f.key === selectedStatus)?.label}訂單`}\n          </p>\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            去購物\n          </Link>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {filteredOrders.map((order) => {\n            const statusInfo = getStatusInfo(order.status);\n            const StatusIcon = statusInfo.icon;\n            \n            return (\n              <div key={order.id} className=\"bg-white rounded-lg border border-gray-200 p-6\">\n                {/* 訂單頭部 */}\n                <div className=\"flex items-center justify-between mb-4 pb-4 border-b border-gray-100\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div>\n                      <p className=\"text-sm text-gray-600\">訂單號</p>\n                      <p className=\"font-medium text-gray-900\">{order.orderNumber}</p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">下單時間</p>\n                      <p className=\"font-medium text-gray-900\">\n                        {new Date(order.createdAt).toLocaleDateString('zh-CN')}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${statusInfo.bgColor}`}>\n                    <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />\n                    <span className={`text-sm font-medium ${statusInfo.color}`}>\n                      {statusInfo.text}\n                    </span>\n                  </div>\n                </div>\n\n                {/* 商品列表 */}\n                <div className=\"space-y-3 mb-4\">\n                  {order.items.slice(0, 3).map((item) => (\n                    <div key={item.id} className=\"flex items-center space-x-3\">\n                      <div className=\"relative w-12 h-12 flex-shrink-0\">\n                        <Image\n                          src={getImageUrl(item.productImage, item.productName)}\n                          alt={item.productName}\n                          fill\n                          style={{ objectFit: \"cover\" }}\n                          className=\"rounded-md\"\n                        />\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <p className=\"text-sm font-medium text-gray-900 line-clamp-1\">\n                          {item.productName}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          ¥{item.price.toFixed(2)} × {item.quantity}\n                        </p>\n                      </div>\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        ¥{item.subtotal.toFixed(2)}\n                      </div>\n                    </div>\n                  ))}\n                  \n                  {order.items.length > 3 && (\n                    <p className=\"text-sm text-gray-500 text-center\">\n                      還有 {order.items.length - 3} 件商品...\n                    </p>\n                  )}\n                </div>\n\n                {/* 訂單底部 */}\n                <div className=\"flex items-center justify-between pt-4 border-t border-gray-100\">\n                  <div className=\"text-sm text-gray-600\">\n                    共 {order.items.reduce((sum, item) => sum + item.quantity, 0)} 件商品\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"text-right\">\n                      <p className=\"text-sm text-gray-600\">訂單總額</p>\n                      <p className=\"text-lg font-semibold text-red-600\">\n                        ¥{order.totalAmount.toFixed(2)}\n                      </p>\n                    </div>\n\n                    <div className=\"flex space-x-2\">\n                      {/* 管理员发货按钮 */}\n                      {isAdmin && canShipOrder(order.status) && (\n                        <button\n                          onClick={() => openShippingModal(order.id!, order.orderNumber)}\n                          className=\"flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\"\n                        >\n                          <TruckIcon className=\"w-4 h-4\" />\n                          <span>發貨</span>\n                        </button>\n                      )}\n\n                      <Link\n                        href={`/orders/${order.id}`}\n                        className=\"flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                      >\n                        <EyeIcon className=\"w-4 h-4\" />\n                        <span>查看詳情</span>\n                      </Link>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      )}\n\n      {/* 返回購物按鈕 */}\n      {filteredOrders.length > 0 && (\n        <div className=\"mt-8 text-center\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\"\n          >\n            繼續購物\n          </Link>\n        </div>\n      )}\n\n      {/* 發貨模態框 */}\n      <ShippingModal\n        isOpen={shippingModal.isOpen}\n        onClose={closeShippingModal}\n        orderId={shippingModal.orderId!}\n        orderNumber={shippingModal.orderNumber!}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAI9C;QAAE,QAAQ;IAAM;IAEnB,yBAAyB;IACzB,MAAM,UAAU,MAAM,gBAAgB;IAEtC,aAAa;IACb,MAAM,eAAe,CAAC;QACpB,OAAO,WAAW,wHAAA,CAAA,cAAW,CAAC,IAAI,IAAI,WAAW,wHAAA,CAAA,cAAW,CAAC,UAAU;IACzE;IAEA,UAAU;IACV,MAAM,oBAAoB,CAAC,SAAiB;QAC1C,iBAAiB;YACf,QAAQ;YACR;YACA;QACF;IACF;IAEA,UAAU;IACV,MAAM,qBAAqB;QACzB,iBAAiB;YAAE,QAAQ;QAAM;IACnC;IAEA,WAAW;IACX,MAAM,iBAAiB,mBAAmB,QACtC,kBACA,kBAAkB;IAEtB,SAAS;IACT,MAAM,gBAAgB;QACpB;YAAE,KAAK;YAAO,OAAO;YAAM,OAAO,OAAO,MAAM;QAAC;QAChD;YAAE,KAAK,wHAAA,CAAA,cAAW,CAAC,OAAO;YAAE,OAAO;YAAO,OAAO,kBAAkB,wHAAA,CAAA,cAAW,CAAC,OAAO,EAAE,MAAM;QAAC;QAC/F;YAAE,KAAK,wHAAA,CAAA,cAAW,CAAC,IAAI;YAAE,OAAO;YAAO,OAAO,kBAAkB,wHAAA,CAAA,cAAW,CAAC,IAAI,EAAE,MAAM;QAAC;QACzF;YAAE,KAAK,wHAAA,CAAA,cAAW,CAAC,OAAO;YAAE,OAAO;YAAO,OAAO,kBAAkB,wHAAA,CAAA,cAAW,CAAC,OAAO,EAAE,MAAM;QAAC;QAC/F;YAAE,KAAK,wHAAA,CAAA,cAAW,CAAC,SAAS;YAAE,OAAO;YAAO,OAAO,kBAAkB,wHAAA,CAAA,cAAW,CAAC,SAAS,EAAE,MAAM;QAAC;QACnG;YAAE,KAAK,wHAAA,CAAA,cAAW,CAAC,SAAS;YAAE,OAAO;YAAO,OAAO,kBAAkB,wHAAA,CAAA,cAAW,CAAC,SAAS,EAAE,MAAM;QAAC;KACpG;IAED,WAAW;IACX,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK,wHAAA,CAAA,cAAW,CAAC,OAAO;gBACtB,OAAO;oBAAE,MAAM;oBAAO,OAAO;oBAAmB,SAAS;oBAAiB,MAAM,oNAAA,CAAA,YAAS;gBAAC;YAC5F,KAAK,wHAAA,CAAA,cAAW,CAAC,IAAI;gBACnB,OAAO;oBAAE,MAAM;oBAAO,OAAO;oBAAiB,SAAS;oBAAe,MAAM,gOAAA,CAAA,kBAAe;gBAAC;YAC9F,KAAK,wHAAA,CAAA,cAAW,CAAC,UAAU;gBACzB,OAAO;oBAAE,MAAM;oBAAO,OAAO;oBAAiB,SAAS;oBAAe,MAAM,oNAAA,CAAA,YAAS;gBAAC;YACxF,KAAK,wHAAA,CAAA,cAAW,CAAC,OAAO;gBACtB,OAAO;oBAAE,MAAM;oBAAO,OAAO;oBAAmB,SAAS;oBAAiB,MAAM,oNAAA,CAAA,YAAS;gBAAC;YAC5F,KAAK,wHAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;oBAAE,MAAM;oBAAO,OAAO;oBAAkB,SAAS;oBAAgB,MAAM,gOAAA,CAAA,kBAAe;gBAAC;YAChG,KAAK,wHAAA,CAAA,cAAW,CAAC,SAAS;gBACxB,OAAO;oBAAE,MAAM;oBAAO,OAAO;oBAAgB,SAAS;oBAAc,MAAM,oNAAA,CAAA,YAAS;gBAAC;YACtF;gBACE,OAAO;oBAAE,MAAM;oBAAM,OAAO;oBAAiB,SAAS;oBAAe,MAAM,oNAAA,CAAA,YAAS;gBAAC;QACzF;IACF;IAEA,UAAU;IACV,MAAM,cAAc,CAAC,KAAa;QAChC,IAAI,CAAC,KAAK,OAAO;QAEjB,IAAI,IAAI,UAAU,CAAC,aAAa;YAC9B,OAAO,CAAC,qDAAqD,EAAE,mBAAmB,cAAc;QAClG;QAEA,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,OAAO,CAAC,qDAAqD,EAAE,mBAAmB,cAAc;QAClG;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAIpC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;4BAEC,SAAS,IAAM,kBAAkB,OAAO,GAAG;4BAC3C,WAAW,CAAC,2DAA2D,EACrE,mBAAmB,OAAO,GAAG,GACzB,2BACA,+CACJ;;gCAED,OAAO,KAAK;gCACZ,OAAO,KAAK,GAAG,mBACd,6LAAC;oCAAK,WAAW,CAAC,KAAK,EACrB,mBAAmB,OAAO,GAAG,GAAG,kBAAkB,iBAClD;;wCAAE;wCACA,OAAO,KAAK;wCAAC;;;;;;;;2BAbd,OAAO,GAAG;;;;;;;;;;;;;;;YAsBtB,eAAe,MAAM,KAAK,kBACzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCACV,mBAAmB,QAAQ,aAAa,CAAC,EAAE,EAAE,cAAc,IAAI,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK,iBAAiB,MAAM,EAAE,CAAC;;;;;;kCAE5G,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;qCAKH,6LAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC;oBACnB,MAAM,aAAa,cAAc,MAAM,MAAM;oBAC7C,MAAM,aAAa,WAAW,IAAI;oBAElC,qBACE,6LAAC;wBAAmB,WAAU;;0CAE5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEAA6B,MAAM,WAAW;;;;;;;;;;;;0DAE7D,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW,OAAO,EAAE;;0DACxF,6LAAC;gDAAW,WAAW,CAAC,QAAQ,EAAE,WAAW,KAAK,EAAE;;;;;;0DACpD,6LAAC;gDAAK,WAAW,CAAC,oBAAoB,EAAE,WAAW,KAAK,EAAE;0DACvD,WAAW,IAAI;;;;;;;;;;;;;;;;;;0CAMtB,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAC5B,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,YAAY,KAAK,YAAY,EAAE,KAAK,WAAW;wDACpD,KAAK,KAAK,WAAW;wDACrB,IAAI;wDACJ,OAAO;4DAAE,WAAW;wDAAQ;wDAC5B,WAAU;;;;;;;;;;;8DAGd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;sEAEnB,6LAAC;4DAAE,WAAU;;gEAAwB;gEACjC,KAAK,KAAK,CAAC,OAAO,CAAC;gEAAG;gEAAI,KAAK,QAAQ;;;;;;;;;;;;;8DAG7C,6LAAC;oDAAI,WAAU;;wDAAoC;wDAC/C,KAAK,QAAQ,CAAC,OAAO,CAAC;;;;;;;;2CAnBlB,KAAK,EAAE;;;;;oCAwBlB,MAAM,KAAK,CAAC,MAAM,GAAG,mBACpB,6LAAC;wCAAE,WAAU;;4CAAoC;4CAC3C,MAAM,KAAK,CAAC,MAAM,GAAG;4CAAE;;;;;;;;;;;;;0CAMjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAwB;4CAClC,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;4CAAG;;;;;;;kDAG/D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,6LAAC;wDAAE,WAAU;;4DAAqC;4DAC9C,MAAM,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;0DAIhC,6LAAC;gDAAI,WAAU;;oDAEZ,WAAW,aAAa,MAAM,MAAM,mBACnC,6LAAC;wDACC,SAAS,IAAM,kBAAkB,MAAM,EAAE,EAAG,MAAM,WAAW;wDAC7D,WAAU;;0EAEV,6LAAC,oNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;0EAAK;;;;;;;;;;;;kEAIV,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;wDAC3B,WAAU;;0EAEV,6LAAC,gNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAzFN,MAAM,EAAE;;;;;gBAgGtB;;;;;;YAKH,eAAe,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;0BAOL,6LAAC,+IAAA,CAAA,UAAa;gBACZ,QAAQ,cAAc,MAAM;gBAC5B,SAAS;gBACT,SAAS,cAAc,OAAO;gBAC9B,aAAa,cAAc,WAAW;;;;;;;;;;;;AAI9C;GArQwB;;QAC+B,6HAAA,CAAA,gBAAa;;;KAD5C", "debugId": null}}]}