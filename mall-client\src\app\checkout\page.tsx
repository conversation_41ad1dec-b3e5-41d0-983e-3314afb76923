'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useCartStore } from '../../store/cartStore';
import { useAddressStore } from '../../store/addressStore';
import { useOrderStore } from '../../store/orderStore';
import { PaymentMethod } from '../../types/order';
import AddressSelector from '../../components/Checkout/AddressSelector';
import PaymentMethodSelector from '../../components/Checkout/PaymentMethodSelector';
import OrderSummary from '../../components/Checkout/OrderSummary';
import toast from 'react-hot-toast';

export default function CheckoutPage() {
  const router = useRouter();
  const { items, totalPrice, clearCart } = useCartStore();
  const { getSelectedAddress } = useAddressStore();
  const { createOrder } = useOrderStore();
  
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [orderNotes, setOrderNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  // 檢查購物車是否為空
  useEffect(() => {
    if (items.length === 0) {
      toast.error('購物車為空，請先添加商品');
      router.push('/cart');
    }
  }, [items, router]);

  // 添加一些默認地址（模擬數據）
  useEffect(() => {
    const { addresses, addAddress } = useAddressStore.getState();
    if (addresses.length === 0) {
      // 添加默認地址
      addAddress({
        recipientName: '張三',
        phone: '13800138000',
        province: '廣東省',
        city: '深圳市',
        district: '南山區',
        detailAddress: '科技園南區深南大道10000號',
        postalCode: '518000',
        isDefault: true
      });
    }
  }, []);

  const selectedAddress = getSelectedAddress();
  const shippingFee = totalPrice >= 99 ? 0 : 10;
  const finalTotal = totalPrice + shippingFee;

  const handleSubmitOrder = async () => {
    // 驗證必填項
    if (!selectedAddress) {
      toast.error('請選擇收貨地址');
      return;
    }

    if (!selectedPaymentMethod) {
      toast.error('請選擇支付方式');
      return;
    }

    if (!agreedToTerms) {
      toast.error('請同意服務條款和隱私政策');
      return;
    }

    setIsSubmitting(true);

    try {
      // 創建訂單
      const orderData = {
        items: items.map(item => ({
          productId: item.id,
          quantity: item.quantity
        })),
        shippingAddressId: selectedAddress.id!,
        paymentMethod: selectedPaymentMethod,
        notes: orderNotes.trim() || undefined
      };

      const order = await createOrder(orderData);
      
      // 清空購物車
      clearCart();
      
      // 顯示成功消息
      toast.success('訂單創建成功！');
      
      // 跳轉到訂單確認頁面
      router.push(`/orders/${order.id}`);
      
    } catch (error) {
      toast.error('創建訂單失敗，請重試');
      console.error('創建訂單失敗:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (items.length === 0) {
    return null; // 避免閃爍，useEffect會處理重定向
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 頁面標題 */}
      <div className="flex items-center mb-6">
        <Link
          href="/cart"
          className="flex items-center text-gray-600 hover:text-gray-800 mr-4"
        >
          <ArrowLeftIcon className="w-5 h-5 mr-1" />
          返回購物車
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">結算</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左側：地址和支付方式 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 收貨地址 */}
          <AddressSelector />
          
          {/* 支付方式 */}
          <PaymentMethodSelector
            selectedMethod={selectedPaymentMethod}
            onMethodSelect={setSelectedPaymentMethod}
          />
          
          {/* 訂單備註 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">訂單備註</h3>
            <textarea
              value={orderNotes}
              onChange={(e) => setOrderNotes(e.target.value)}
              placeholder="如有特殊要求，請在此說明（選填）"
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
              maxLength={200}
            />
            <div className="text-right text-xs text-gray-500 mt-1">
              {orderNotes.length}/200
            </div>
          </div>
        </div>

        {/* 右側：訂單摘要 */}
        <div className="lg:col-span-1">
          <div className="sticky top-4 space-y-6">
            <OrderSummary />
            
            {/* 服務條款 */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <label className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  checked={agreedToTerms}
                  onChange={(e) => setAgreedToTerms(e.target.checked)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-600">
                  我已閱讀並同意
                  <Link href="/terms" className="text-blue-600 hover:text-blue-800 mx-1">
                    服務條款
                  </Link>
                  和
                  <Link href="/privacy" className="text-blue-600 hover:text-blue-800 mx-1">
                    隱私政策
                  </Link>
                </span>
              </label>
            </div>
            
            {/* 提交訂單按鈕 */}
            <button
              onClick={handleSubmitOrder}
              disabled={isSubmitting || !selectedAddress || !selectedPaymentMethod || !agreedToTerms}
              className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors ${
                isSubmitting || !selectedAddress || !selectedPaymentMethod || !agreedToTerms
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-red-600 text-white hover:bg-red-700'
              }`}
            >
              {isSubmitting ? '提交中...' : `確認訂單 ¥${finalTotal.toFixed(2)}`}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
