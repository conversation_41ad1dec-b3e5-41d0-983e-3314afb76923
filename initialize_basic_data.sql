-- 初始化基礎數據腳本
USE mall;

-- 1. 初始化角色數據
INSERT IGNORE INTO roles (name, description) VALUES 
('USER', '普通用戶，可以瀏覽商品、下單購買'),
('ADMIN', '管理員，擁有系統管理權限'),
('SELLER', '商家，可以管理自己的商品'),
('MODERATOR', '版主，可以管理內容和用戶');

-- 2. 初始化訂單狀態數據
INSERT IGNORE INTO order_statuses (status_name, description) VALUES 
('PENDING', '待付款 - 訂單已創建，等待付款'),
('PAID', '已付款 - 訂單已付款，等待處理'),
('PROCESSING', '處理中 - 訂單正在處理中'),
('SHIPPED', '已發貨 - 訂單已發貨，正在運輸中'),
('DELIVERED', '已送達 - 訂單已成功送達'),
('CANCELLED', '已取消 - 訂單已被取消'),
('REFUNDED', '已退款 - 訂單已退款');

-- 3. 為現有用戶分配默認角色（如果有用戶的話）
-- 首先檢查是否有用戶沒有角色
INSERT IGNORE INTO user_roles (user_id, role_id)
SELECT u.id, r.id 
FROM users u
CROSS JOIN roles r
WHERE r.name = 'USER'
AND NOT EXISTS (
    SELECT 1 FROM user_roles ur WHERE ur.user_id = u.id
);

-- 4. 檢查結果
SELECT 'Roles initialized:' as message;
SELECT * FROM roles;

SELECT 'Order statuses initialized:' as message;
SELECT * FROM order_statuses;

SELECT 'User roles assigned:' as message;
SELECT u.username, r.name as role_name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id;
