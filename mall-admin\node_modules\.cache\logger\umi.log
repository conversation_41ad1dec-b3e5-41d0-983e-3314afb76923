{"level":30,"time":1748612453786,"pid":54428,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] max g component 可以快速生成组件模板，详见 https://umijs.org/docs/guides/generator#组件生成器\u001b[39m"}
{"level":30,"time":1748612453795,"pid":54428,"hostname":"desktopRo","msg":"generate files"}
{"level":30,"time":1748612456763,"pid":54428,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748612588478,"pid":56968,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果想点击组件跳转至编辑器源码位置，可尝试新出的 clickToComponent 配置项，详见 https://umijs.org/docs/api/config#clicktocomponent\u001b[39m"}
{"level":30,"time":1748612588786,"pid":56968,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":30,"time":1748612588837,"pid":56968,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":30,"time":1748612609326,"pid":46180,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1748612609327,"pid":46180,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748612610118,"pid":46180,"hostname":"desktopRo","msg":"Preparing..."}
{"level":60,"time":1748612610259,"pid":46180,"hostname":"desktopRo","err":{"type":"Error","message":"Build failed with 6 errors:\nsrc/components/RightContent/AvatarDropdown.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"outLogin\"\nsrc/pages/TableList/index.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"addRule\"\nsrc/pages/TableList/index.tsx:1:18: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"removeRule\"\nsrc/pages/TableList/index.tsx:1:30: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"rule\"\nsrc/pages/TableList/index.tsx:1:36: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"updateRule\"\n...","stack":"Error: Build failed with 6 errors:\nsrc/components/RightContent/AvatarDropdown.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"outLogin\"\nsrc/pages/TableList/index.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"addRule\"\nsrc/pages/TableList/index.tsx:1:18: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"removeRule\"\nsrc/pages/TableList/index.tsx:1:30: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"rule\"\nsrc/pages/TableList/index.tsx:1:36: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"updateRule\"\n...\n    at failureErrorWithLog (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\esbuild@0.21.4\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\esbuild@0.21.4\\node_modules\\esbuild\\lib\\main.js:945:25\n    at C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\esbuild@0.21.4\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":9,"file":"src/components/RightContent/AvatarDropdown.tsx","length":8,"line":1,"lineText":"import { outLogin } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"outLogin\""},{"id":"","location":{"column":9,"file":"src/pages/TableList/index.tsx","length":7,"line":1,"lineText":"import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"addRule\""},{"id":"","location":{"column":18,"file":"src/pages/TableList/index.tsx","length":10,"line":1,"lineText":"import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"removeRule\""},{"id":"","location":{"column":30,"file":"src/pages/TableList/index.tsx","length":4,"line":1,"lineText":"import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"rule\""},{"id":"","location":{"column":36,"file":"src/pages/TableList/index.tsx","length":10,"line":1,"lineText":"import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"updateRule\""},{"id":"","location":{"column":9,"file":"src/pages/User/Login/index.tsx","length":5,"line":2,"lineText":"import { login } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"login\""}],"warnings":[]},"msg":"Build failed with 6 errors:\nsrc/components/RightContent/AvatarDropdown.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"outLogin\"\nsrc/pages/TableList/index.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"addRule\"\nsrc/pages/TableList/index.tsx:1:18: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"removeRule\"\nsrc/pages/TableList/index.tsx:1:30: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"rule\"\nsrc/pages/TableList/index.tsx:1:36: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"updateRule\"\n..."}
{"level":60,"time":1748612610276,"pid":46180,"hostname":"desktopRo","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1748612610292,"pid":46180,"hostname":"desktopRo","msg":"C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1748612610308,"pid":46180,"hostname":"desktopRo","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1748612649992,"pid":21600,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 需要添加全局 React Context 吗？在 src/app.(ts|tsx) 运行时配置中轻松解决，详见 https://umijs.org/docs/api/runtime-config\u001b[39m"}
{"level":30,"time":1748612650274,"pid":21600,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":30,"time":1748612650389,"pid":21600,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":30,"time":1748612664865,"pid":50668,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1748612664867,"pid":50668,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748612665678,"pid":50668,"hostname":"desktopRo","msg":"Preparing..."}
{"level":60,"time":1748612665827,"pid":50668,"hostname":"desktopRo","err":{"type":"Error","message":"Build failed with 6 errors:\nsrc/components/RightContent/AvatarDropdown.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"outLogin\"\nsrc/pages/TableList/index.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"addRule\"\nsrc/pages/TableList/index.tsx:1:18: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"removeRule\"\nsrc/pages/TableList/index.tsx:1:30: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"rule\"\nsrc/pages/TableList/index.tsx:1:36: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"updateRule\"\n...","stack":"Error: Build failed with 6 errors:\nsrc/components/RightContent/AvatarDropdown.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"outLogin\"\nsrc/pages/TableList/index.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"addRule\"\nsrc/pages/TableList/index.tsx:1:18: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"removeRule\"\nsrc/pages/TableList/index.tsx:1:30: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"rule\"\nsrc/pages/TableList/index.tsx:1:36: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"updateRule\"\n...\n    at failureErrorWithLog (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\esbuild@0.21.4\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\esbuild@0.21.4\\node_modules\\esbuild\\lib\\main.js:945:25\n    at C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\esbuild@0.21.4\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":9,"file":"src/components/RightContent/AvatarDropdown.tsx","length":8,"line":1,"lineText":"import { outLogin } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"outLogin\""},{"id":"","location":{"column":9,"file":"src/pages/TableList/index.tsx","length":7,"line":1,"lineText":"import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"addRule\""},{"id":"","location":{"column":18,"file":"src/pages/TableList/index.tsx","length":10,"line":1,"lineText":"import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"removeRule\""},{"id":"","location":{"column":30,"file":"src/pages/TableList/index.tsx","length":4,"line":1,"lineText":"import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"rule\""},{"id":"","location":{"column":36,"file":"src/pages/TableList/index.tsx","length":10,"line":1,"lineText":"import { addRule, removeRule, rule, updateRule } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"updateRule\""},{"id":"","location":{"column":9,"file":"src/pages/User/Login/index.tsx","length":5,"line":2,"lineText":"import { login } from '@/services/ant-design-pro/api';","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"No matching export in \"src/services/ant-design-pro/api.ts\" for import \"login\""}],"warnings":[]},"msg":"Build failed with 6 errors:\nsrc/components/RightContent/AvatarDropdown.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"outLogin\"\nsrc/pages/TableList/index.tsx:1:9: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"addRule\"\nsrc/pages/TableList/index.tsx:1:18: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"removeRule\"\nsrc/pages/TableList/index.tsx:1:30: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"rule\"\nsrc/pages/TableList/index.tsx:1:36: ERROR: No matching export in \"src/services/ant-design-pro/api.ts\" for import \"updateRule\"\n..."}
{"level":60,"time":1748612666702,"pid":50668,"hostname":"desktopRo","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1748612666713,"pid":50668,"hostname":"desktopRo","msg":"C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1748612666753,"pid":50668,"hostname":"desktopRo","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1748612807919,"pid":50752,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1748612807921,"pid":50752,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748612808633,"pid":50752,"hostname":"desktopRo","msg":"Preparing..."}
{"level":20,"time":1748612810843,"pid":50752,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748612810875,"pid":50752,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748612813747,"pid":50752,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2903 ms (524 modules)"}
{"level":30,"time":1748612813750,"pid":50752,"hostname":"desktopRo","msg":"[MFSU] buildDeps since cacheDependency has changed"}
{"level":20,"time":1748612813750,"pid":50752,"hostname":"desktopRo","msg":"C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/dist/reset.css, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/asyncToGenerator.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/objectSpread2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/regeneratorRuntime.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react/jsx-dev-runtime, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/umi@4.4.11_@babel+core@7.4._97aef5f84ba3bed6a7b4ab93e4bba605/node_modules/umi/client/client/plugin.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/duration, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localizedFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localeData, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isMoment, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekOfYear, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekYear, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekday, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/customParseFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/advancedFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrAfter, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrBefore, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/antd-dayjs-webpack-plugin@1.0.6_dayjs@1.11.13/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/typeof.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/regenerator-runtime@0.13.11/node_modules/regenerator-runtime/runtime.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.size.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.has.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.delete.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url.can-parse.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.structured-clone.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.self.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.immediate.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.dom-exception.stack.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.add-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.upsert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.emplace.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.to-hex.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.to-base64.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.from-hex.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.from-base64.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.unique-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.to-spliced.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.filter-reject.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.filter-out.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.from-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.replace-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.pattern-match.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.observable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.metadata-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.matcher.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-well-known.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-well-known-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-registered.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-registered-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.async-dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.dedent.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.code-points.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.cooked.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.union.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.union.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.symmetric-difference.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.symmetric-difference.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.join.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-superset-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-superset-of.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-subset-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-subset-of.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-disjoint-from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-disjoint-from.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.intersection.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.intersection.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.difference.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.difference.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.add-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.regexp.escape.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.has-own-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.has-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-own-metadata-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-own-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-metadata-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.delete-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.define-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.promise.try.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.observable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-values.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-entries.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.number.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.number.from-string.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.umulh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.signbit.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.seeded-prng.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.scale.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.radians.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.rad-per-deg.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.isubh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.imulh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.iaddh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.f16round.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.fscale.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.degrees.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.deg-per-rad.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.clamp.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.upsert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.update-or-insert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.update.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.merge.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.map-values.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.map-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.key-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.key-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.includes.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.find-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.emplace.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.raw-json.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.parse.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.is-raw-json.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.to-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.to-array.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.take.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.indexed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.for-each.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.flat-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.drop.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.as-indexed-pairs.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.un-this.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.is-constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.is-callable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.demethodize.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.disposable-stack.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.set-uint8-clamped.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.set-float16.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.get-uint8-clamped.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.get-float16.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.composite-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.composite-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.bigint.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.to-array.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.take.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.indexed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.for-each.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.flat-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.drop.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.async-dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.as-indexed-pairs.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-disposable-stack.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.transfer-to-fixed-length.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.transfer.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.detached.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.unique-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.last-item.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.is-template-object.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-to-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-by-to-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.filter-reject.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.filter-out.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.from-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.suppressed-error.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.with.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.to-sorted.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.to-reversed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.set.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.find-last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.find-last.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.to-well-formed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.replace-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.is-well-formed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.at-alternative.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.regexp.flags.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.reflect.to-string-tag.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.promise.with-resolvers.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.promise.any.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.object.has-own.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.object.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.map.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.with.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-spliced.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-sorted.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-reversed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.reduce-right.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.push.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.find-last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.find-last.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.aggregate-error.cause.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.aggregate-error.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.error.cause.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/slicedToArray.js, @ant-design/pro-components, @ant-design/icons, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/@ant-design/pro-components, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/toConsumableArray.js, swagger-ui-dist/swagger-ui.css, swagger-ui-dist, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/fast-deep-equal@3.1.3/node_modules/fast-deep-equal/index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/defineProperty.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/classCallCheck.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/createClass.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/objectWithoutProperties.js, antd-style, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react-dom, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/TableOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/CrownOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/SmileOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ahooksjs+use-request@2.8.15_react@18.3.1/node_modules/@ahooksjs/use-request, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/axios@0.27.2/node_modules/axios, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/zh_TW, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/zh_CN, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/pt_BR, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/ja_JP, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/id_ID, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/fa_IR, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/en_US, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/bn_BD, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/react-intl@3.12.1_react@18.3.1/node_modules/react-intl, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/warning@4.0.3/node_modules/warning, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/event-emitter@0.3.5/node_modules/event-emitter, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-tw, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-cn, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/pt-br, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ja, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/id, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/fa, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/en, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/bn-bd, querystring, classnames"}
{"level":55,"time":1748612813884,"pid":50752,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748612813885,"pid":50752,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748612814016,"pid":50752,"hostname":"desktopRo","msg":"[Webpack] Compiled in 131 ms (498 modules)"}
{"level":55,"time":1748612814044,"pid":50752,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748612814045,"pid":50752,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748612814125,"pid":50752,"hostname":"desktopRo","msg":"[Webpack] Compiled in 81 ms (498 modules)"}
{"level":30,"time":1748612821768,"pid":20760,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] COMPRESS=none max build 可以关闭项目构建时的代码压缩功能, 方便调试项目的构建产物。\u001b[39m"}
{"level":30,"time":1748612821769,"pid":20760,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748612822570,"pid":20760,"hostname":"desktopRo","msg":"Preparing..."}
{"level":20,"time":1748612825071,"pid":20760,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748612825101,"pid":20760,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748612828279,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3207 ms (524 modules)"}
{"level":30,"time":1748612828282,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] buildDeps since cacheDependency has changed"}
{"level":20,"time":1748612828283,"pid":20760,"hostname":"desktopRo","msg":"C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/dist/reset.css, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/asyncToGenerator.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/objectSpread2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/regeneratorRuntime.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react/jsx-dev-runtime, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/umi@4.4.11_@babel+core@7.4._97aef5f84ba3bed6a7b4ab93e4bba605/node_modules/umi/client/client/plugin.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/duration, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localizedFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localeData, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isMoment, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekOfYear, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekYear, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekday, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/customParseFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/advancedFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrAfter, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrBefore, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/antd-dayjs-webpack-plugin@1.0.6_dayjs@1.11.13/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/typeof.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/regenerator-runtime@0.13.11/node_modules/regenerator-runtime/runtime.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.size.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.has.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.delete.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url.can-parse.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.structured-clone.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.self.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.immediate.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.dom-exception.stack.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.add-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.upsert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.emplace.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.to-hex.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.to-base64.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.from-hex.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.from-base64.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.unique-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.to-spliced.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.filter-reject.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.filter-out.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.from-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.replace-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.pattern-match.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.observable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.metadata-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.matcher.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-well-known.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-well-known-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-registered.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-registered-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.async-dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.dedent.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.code-points.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.cooked.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.union.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.union.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.symmetric-difference.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.symmetric-difference.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.join.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-superset-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-superset-of.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-subset-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-subset-of.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-disjoint-from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-disjoint-from.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.intersection.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.intersection.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.difference.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.difference.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.add-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.regexp.escape.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.has-own-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.has-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-own-metadata-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-own-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-metadata-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.delete-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.define-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.promise.try.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.observable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-values.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-entries.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.number.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.number.from-string.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.umulh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.signbit.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.seeded-prng.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.scale.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.radians.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.rad-per-deg.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.isubh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.imulh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.iaddh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.f16round.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.fscale.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.degrees.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.deg-per-rad.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.clamp.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.upsert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.update-or-insert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.update.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.merge.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.map-values.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.map-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.key-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.key-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.includes.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.find-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.emplace.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.raw-json.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.parse.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.is-raw-json.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.to-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.to-array.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.take.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.indexed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.for-each.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.flat-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.drop.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.as-indexed-pairs.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.un-this.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.is-constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.is-callable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.demethodize.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.disposable-stack.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.set-uint8-clamped.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.set-float16.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.get-uint8-clamped.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.get-float16.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.composite-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.composite-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.bigint.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.to-array.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.take.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.indexed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.for-each.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.flat-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.drop.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.async-dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.as-indexed-pairs.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-disposable-stack.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.transfer-to-fixed-length.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.transfer.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.detached.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.unique-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.last-item.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.is-template-object.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-to-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-by-to-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.filter-reject.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.filter-out.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.from-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.suppressed-error.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.with.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.to-sorted.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.to-reversed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.set.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.find-last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.find-last.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.to-well-formed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.replace-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.is-well-formed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.at-alternative.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.regexp.flags.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.reflect.to-string-tag.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.promise.with-resolvers.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.promise.any.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.object.has-own.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.object.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.map.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.with.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-spliced.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-sorted.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-reversed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.reduce-right.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.push.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.find-last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.find-last.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.aggregate-error.cause.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.aggregate-error.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.error.cause.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react, @ant-design/pro-components, @ant-design/icons, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/slicedToArray.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/@ant-design/pro-components, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/toConsumableArray.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/fast-deep-equal@3.1.3/node_modules/fast-deep-equal/index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/defineProperty.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/classCallCheck.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/createClass.js, swagger-ui-dist/swagger-ui.css, swagger-ui-dist, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/objectWithoutProperties.js, antd-style, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react-dom, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/TableOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/CrownOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/SmileOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-tw, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-cn, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/pt-br, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ja, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/id, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/fa, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/en, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/bn-bd, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/zh_TW, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/zh_CN, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/pt_BR, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/ja_JP, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/id_ID, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/fa_IR, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/en_US, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/bn_BD, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/react-intl@3.12.1_react@18.3.1/node_modules/react-intl, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/warning@4.0.3/node_modules/warning, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/event-emitter@0.3.5/node_modules/event-emitter, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ahooksjs+use-request@2.8.15_react@18.3.1/node_modules/@ahooksjs/use-request, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/axios@0.27.2/node_modules/axios, querystring, classnames"}
{"level":55,"time":1748612828399,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748612828399,"pid":20760,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748612828600,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 200 ms (498 modules)"}
{"level":55,"time":1748612828631,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748612828632,"pid":20760,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748612828752,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 120 ms (498 modules)"}
{"level":32,"time":1748612841090,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 11963 ms (5713 modules)"}
{"level":30,"time":1748612841117,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] write cache"}
{"level":30,"time":1748612841117,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] buildDepsAgain"}
{"level":30,"time":1748612841119,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748612913026,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748612913060,"pid":20760,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748612917108,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 4043 ms (499 modules)"}
{"level":30,"time":1748612917160,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748613011750,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748613011786,"pid":20760,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748613012043,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 257 ms (499 modules)"}
{"level":30,"time":1748613012045,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748613175333,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748613175359,"pid":20760,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748613175724,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 365 ms (499 modules)"}
{"level":30,"time":1748613175742,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748613206712,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748613206737,"pid":20760,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748613207084,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 347 ms (499 modules)"}
{"level":30,"time":1748613207092,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748613265287,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748613265313,"pid":20760,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748613265589,"pid":20760,"hostname":"desktopRo","msg":"[Webpack] Compiled in 276 ms (499 modules)"}
{"level":30,"time":1748613265592,"pid":20760,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748613410127,"pid":14144,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 请求加载态、数据管理、避免竟态问题，用 react-query 帮你全部解决，详见 https://umijs.org/docs/max/react-query\u001b[39m"}
{"level":30,"time":1748613410128,"pid":14144,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748613410916,"pid":14144,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748613412922,"pid":14144,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748613413579,"pid":14144,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748613413611,"pid":14144,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748613416001,"pid":14144,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2419 ms (513 modules)"}
{"level":30,"time":1748613416003,"pid":14144,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748613416088,"pid":14144,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748613416088,"pid":14144,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748613416216,"pid":14144,"hostname":"desktopRo","msg":"[Webpack] Compiled in 127 ms (499 modules)"}
{"level":30,"time":1748613416217,"pid":14144,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748613416246,"pid":14144,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748613416248,"pid":14144,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748613416341,"pid":14144,"hostname":"desktopRo","msg":"[Webpack] Compiled in 95 ms (499 modules)"}
{"level":30,"time":1748613416343,"pid":14144,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748613504992,"pid":25852,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1748613504994,"pid":25852,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748613505749,"pid":25852,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748613507600,"pid":25852,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748613508234,"pid":25852,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748613508263,"pid":25852,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748613510728,"pid":25852,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2491 ms (513 modules)"}
{"level":30,"time":1748613510730,"pid":25852,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748613510819,"pid":25852,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748613510820,"pid":25852,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748613510976,"pid":25852,"hostname":"desktopRo","msg":"[Webpack] Compiled in 156 ms (499 modules)"}
{"level":30,"time":1748613510978,"pid":25852,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748613511011,"pid":25852,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748613511013,"pid":25852,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748613511132,"pid":25852,"hostname":"desktopRo","msg":"[Webpack] Compiled in 120 ms (499 modules)"}
{"level":30,"time":1748613511133,"pid":25852,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748614661200,"pid":51848,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1748614661203,"pid":51848,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748614662939,"pid":51848,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748614664116,"pid":51848,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748614665653,"pid":51848,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748614665748,"pid":51848,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748614669316,"pid":51848,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3661 ms (513 modules)"}
{"level":30,"time":1748614669320,"pid":51848,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614669460,"pid":51848,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614669461,"pid":51848,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614669678,"pid":51848,"hostname":"desktopRo","msg":"[Webpack] Compiled in 217 ms (499 modules)"}
{"level":30,"time":1748614669679,"pid":51848,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614669715,"pid":51848,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614669716,"pid":51848,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614669874,"pid":51848,"hostname":"desktopRo","msg":"[Webpack] Compiled in 159 ms (499 modules)"}
{"level":30,"time":1748614669877,"pid":51848,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748614739515,"pid":3696,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1748614739517,"pid":3696,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748614740322,"pid":3696,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748614742131,"pid":3696,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748614742750,"pid":3696,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748614742785,"pid":3696,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748614745344,"pid":3696,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2591 ms (513 modules)"}
{"level":30,"time":1748614745346,"pid":3696,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614745438,"pid":3696,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614745439,"pid":3696,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614745574,"pid":3696,"hostname":"desktopRo","msg":"[Webpack] Compiled in 135 ms (499 modules)"}
{"level":30,"time":1748614745576,"pid":3696,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614745616,"pid":3696,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614745618,"pid":3696,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614745760,"pid":3696,"hostname":"desktopRo","msg":"[Webpack] Compiled in 142 ms (499 modules)"}
{"level":30,"time":1748614745761,"pid":3696,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748614766768,"pid":6660,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Prettier, max g prettier 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#prettier-配置生成器\u001b[39m"}
{"level":30,"time":1748614766770,"pid":6660,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748614767622,"pid":6660,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748614769733,"pid":6660,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748614770397,"pid":6660,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748614770435,"pid":6660,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748614774460,"pid":6660,"hostname":"desktopRo","msg":"[Webpack] Compiled in 4060 ms (513 modules)"}
{"level":30,"time":1748614774463,"pid":6660,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614774578,"pid":6660,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614774579,"pid":6660,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614774743,"pid":6660,"hostname":"desktopRo","msg":"[Webpack] Compiled in 163 ms (499 modules)"}
{"level":30,"time":1748614774744,"pid":6660,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614774788,"pid":6660,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614774790,"pid":6660,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614774907,"pid":6660,"hostname":"desktopRo","msg":"[Webpack] Compiled in 118 ms (499 modules)"}
{"level":30,"time":1748614774909,"pid":6660,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748614869080,"pid":7928,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1748614869081,"pid":7928,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748614869988,"pid":7928,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748614872182,"pid":7928,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748614873466,"pid":7928,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748614873520,"pid":7928,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748614877073,"pid":7928,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3605 ms (513 modules)"}
{"level":30,"time":1748614877078,"pid":7928,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614877207,"pid":7928,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614877208,"pid":7928,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614877381,"pid":7928,"hostname":"desktopRo","msg":"[Webpack] Compiled in 173 ms (499 modules)"}
{"level":30,"time":1748614877383,"pid":7928,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614877436,"pid":7928,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614877438,"pid":7928,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614877577,"pid":7928,"hostname":"desktopRo","msg":"[Webpack] Compiled in 140 ms (499 modules)"}
{"level":30,"time":1748614877579,"pid":7928,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748614899830,"pid":56892,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints\u001b[39m"}
{"level":30,"time":1748614899832,"pid":56892,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748614900653,"pid":56892,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748614902569,"pid":56892,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748614903202,"pid":56892,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748614903234,"pid":56892,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748614906889,"pid":56892,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3685 ms (513 modules)"}
{"level":30,"time":1748614906891,"pid":56892,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614907013,"pid":56892,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614907014,"pid":56892,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614907181,"pid":56892,"hostname":"desktopRo","msg":"[Webpack] Compiled in 167 ms (499 modules)"}
{"level":30,"time":1748614907183,"pid":56892,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614907223,"pid":56892,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614907224,"pid":56892,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614907312,"pid":56892,"hostname":"desktopRo","msg":"[Webpack] Compiled in 89 ms (499 modules)"}
{"level":30,"time":1748614907314,"pid":56892,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614940490,"pid":56892,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614940524,"pid":56892,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614940921,"pid":56892,"hostname":"desktopRo","msg":"[Webpack] Compiled in 398 ms (499 modules)"}
{"level":30,"time":1748614940923,"pid":56892,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748614984298,"pid":47964,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1748614984301,"pid":47964,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748614985834,"pid":47964,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748614986906,"pid":47964,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748614988215,"pid":47964,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748614988306,"pid":47964,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748614991638,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3425 ms (513 modules)"}
{"level":30,"time":1748614991641,"pid":47964,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614991760,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614991760,"pid":47964,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614991933,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiled in 172 ms (499 modules)"}
{"level":30,"time":1748614991935,"pid":47964,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748614991980,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748614991982,"pid":47964,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748614992130,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiled in 149 ms (499 modules)"}
{"level":30,"time":1748614992132,"pid":47964,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748690887394,"pid":52884,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1748690887721,"pid":52884,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":55,"time":1748690887452,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748690887455,"pid":47964,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748690887763,"pid":47964,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":55,"time":1748690887773,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748690887799,"pid":47964,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748690887961,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiled in 163 ms (499 modules)"}
{"level":30,"time":1748690887963,"pid":47964,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748690964633,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748690964660,"pid":47964,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748690965334,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiled in 673 ms (499 modules)"}
{"level":30,"time":1748690965336,"pid":47964,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748691017609,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748691017639,"pid":47964,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748691017911,"pid":47964,"hostname":"desktopRo","msg":"[Webpack] Compiled in 272 ms (499 modules)"}
{"level":30,"time":1748691017913,"pid":47964,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748691155508,"pid":60112,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 页面加载慢、产物体积大怎么办？试试做代码拆分，详见 https://umijs.org/blog/code-splitting\u001b[39m"}
{"level":30,"time":1748691155510,"pid":60112,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748691156286,"pid":60112,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748691158429,"pid":60112,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748691159221,"pid":60112,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748691159252,"pid":60112,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748691161780,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2558 ms (513 modules)"}
{"level":30,"time":1748691161783,"pid":60112,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748691161898,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748691161899,"pid":60112,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748691162043,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiled in 144 ms (499 modules)"}
{"level":30,"time":1748691162045,"pid":60112,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748691162080,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748691162082,"pid":60112,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748691162181,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiled in 100 ms (499 modules)"}
{"level":30,"time":1748691162183,"pid":60112,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748694287029,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748694287060,"pid":60112,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748694287496,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiled in 434 ms (499 modules)"}
{"level":30,"time":1748694287519,"pid":60112,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748699841165,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748699841170,"pid":60112,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":30,"time":1748699840728,"pid":61312,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":50,"time":1748699841520,"pid":60112,"hostname":"desktopRo","msg":"./src/app.tsx:14:0-70"}
{"level":50,"time":1748699841520,"pid":60112,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":55,"time":1748699841529,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748699841554,"pid":60112,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":30,"time":1748699841471,"pid":61312,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":32,"time":1748699841758,"pid":60112,"hostname":"desktopRo","msg":"[Webpack] Compiled in 205 ms (499 modules)"}
{"level":30,"time":1748699841760,"pid":60112,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748699890500,"pid":17396,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1748699890787,"pid":17396,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":30,"time":1748699895455,"pid":59564,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果你有 MPA（多页应用）需求，可尝试新出的 mpa 配置项，详见 https://umijs.org/docs/guides/mpa\u001b[39m"}
{"level":30,"time":1748699895457,"pid":59564,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748699896210,"pid":59564,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748699898216,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748699898938,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748699898971,"pid":59564,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748699901210,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2270 ms (513 modules)"}
{"level":30,"time":1748699901213,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748699901300,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748699901302,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748699901409,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 107 ms (499 modules)"}
{"level":30,"time":1748699901410,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748699901441,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748699901442,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748699901513,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 72 ms (499 modules)"}
{"level":30,"time":1748699901515,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748700143081,"pid":59564,"hostname":"desktopRo","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":55,"time":1748700143693,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748700143746,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748700145938,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2200 ms (501 modules)"}
{"level":30,"time":1748700145976,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748700162745,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748700162783,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748700162957,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 175 ms (501 modules)"}
{"level":30,"time":1748700162959,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748700239815,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748700239844,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748700240100,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 256 ms (501 modules)"}
{"level":30,"time":1748700240102,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748701504763,"pid":4640,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1748701505748,"pid":4640,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":55,"time":1748701505312,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748701505314,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748701505659,"pid":59564,"hostname":"desktopRo","msg":"./src/app.tsx:14:0-70"}
{"level":50,"time":1748701505660,"pid":59564,"hostname":"desktopRo","msg":"./src/pages/ProductList/index.tsx:10:0-70"}
{"level":50,"time":1748701505660,"pid":59564,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":55,"time":1748701505714,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748701505739,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748701506069,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 331 ms (501 modules)"}
{"level":30,"time":1748701506071,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748701506077,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748701506100,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748701506133,"pid":59564,"hostname":"desktopRo","msg":"[icons] build failed: Error: Build failed with 2 errors:\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/authController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src'\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/authController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\User\\Login'"}
{"level":32,"time":1748701506188,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 89 ms (501 modules)"}
{"level":30,"time":1748701506190,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748701579673,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748701579698,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748701580118,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 416 ms (501 modules)"}
{"level":30,"time":1748701580134,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748701890805,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748701890835,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748701891150,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 316 ms (501 modules)"}
{"level":30,"time":1748701891153,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748701901991,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748701902015,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748701902348,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 334 ms (501 modules)"}
{"level":30,"time":1748701902350,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748702013315,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748702013343,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748702013721,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 376 ms (501 modules)"}
{"level":30,"time":1748702013728,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748787585036,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748787585043,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748787585445,"pid":59564,"hostname":"desktopRo","msg":"./src/app.tsx:14:0-70"}
{"level":50,"time":1748787585445,"pid":59564,"hostname":"desktopRo","msg":"./src/pages/ProductList/index.tsx:12:0-115"}
{"level":50,"time":1748787585445,"pid":59564,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":55,"time":1748787585454,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748787585479,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":30,"time":1748787584955,"pid":27828,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] max g page 可以快速生成页面模板，详见 https://umijs.org/docs/guides/generator#页面生成器\u001b[39m"}
{"level":30,"time":1748787585357,"pid":27828,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":32,"time":1748787585694,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 217 ms (501 modules)"}
{"level":30,"time":1748787585696,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748787615039,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748787615070,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748787615614,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 545 ms (501 modules)"}
{"level":30,"time":1748787615617,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748787927832,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748787927867,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748787928176,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 310 ms (501 modules)"}
{"level":30,"time":1748787928178,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748787930391,"pid":61248,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1748787964720,"pid":38640,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果你有 MPA（多页应用）需求，可尝试新出的 mpa 配置项，详见 https://umijs.org/docs/guides/mpa\u001b[39m"}
{"level":30,"time":1748787965934,"pid":38640,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":55,"time":1748787965589,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748787965590,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748787965748,"pid":59564,"hostname":"desktopRo","msg":"./src/app.tsx:14:0-70"}
{"level":50,"time":1748787965749,"pid":59564,"hostname":"desktopRo","msg":"./src/pages/ProductList/index.tsx:12:0-115"}
{"level":50,"time":1748787965749,"pid":59564,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":50,"time":1748787965884,"pid":59564,"hostname":"desktopRo","msg":"[icons] build failed: Error: Build failed with 3 errors:\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/authController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src'\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/authController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\User\\Login'\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/productController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\ProductList'"}
{"level":55,"time":1748787965913,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748787965955,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748787966130,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 176 ms (501 modules)"}
{"level":30,"time":1748787966132,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748787984915,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748787984940,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748787985429,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 489 ms (501 modules)"}
{"level":30,"time":1748787985430,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748788004324,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748788004348,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748788004611,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 264 ms (501 modules)"}
{"level":30,"time":1748788004613,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748788031210,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748788031234,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748788031513,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 268 ms (501 modules)"}
{"level":30,"time":1748788031546,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748788060248,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748788060284,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748788061180,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 880 ms (501 modules)"}
{"level":30,"time":1748788061202,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748788237941,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748788237967,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748788238193,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 226 ms (501 modules)"}
{"level":30,"time":1748788238196,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748788243115,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748788243142,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748788243309,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 168 ms (501 modules)"}
{"level":30,"time":1748788243310,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748788331319,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748788331358,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748788331579,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 220 ms (501 modules)"}
{"level":30,"time":1748788331581,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748788363329,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748788363355,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748788363887,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 523 ms (501 modules)"}
{"level":30,"time":1748788363929,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748788474056,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748788474084,"pid":59564,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748788474633,"pid":59564,"hostname":"desktopRo","msg":"[Webpack] Compiled in 548 ms (501 modules)"}
{"level":30,"time":1748788474646,"pid":59564,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748938613326,"pid":22276,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1748938613327,"pid":22276,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748938614259,"pid":22276,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748938616085,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748938618053,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748938618112,"pid":22276,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748938622006,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3953 ms (515 modules)"}
{"level":30,"time":1748938622009,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748938622129,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748938622130,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748938622285,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 155 ms (501 modules)"}
{"level":30,"time":1748938622287,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748938622325,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748938622327,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748938622476,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 151 ms (501 modules)"}
{"level":30,"time":1748938622478,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748957919665,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748957919668,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748957919987,"pid":22276,"hostname":"desktopRo","msg":"./src/app.tsx:14:0-70"}
{"level":50,"time":1748957919988,"pid":22276,"hostname":"desktopRo","msg":"./src/pages/ProductList/index.tsx:13:0-115"}
{"level":50,"time":1748957919988,"pid":22276,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":55,"time":1748957920011,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748957920036,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":30,"time":1748957919606,"pid":3068,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] max g component 可以快速生成组件模板，详见 https://umijs.org/docs/guides/generator#组件生成器\u001b[39m"}
{"level":30,"time":1748957920065,"pid":3068,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":32,"time":1748957920344,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 310 ms (501 modules)"}
{"level":30,"time":1748957920346,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748957920355,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748957920378,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748957920513,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 136 ms (501 modules)"}
{"level":30,"time":1748957920515,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748958326033,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958326036,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748958326246,"pid":22276,"hostname":"desktopRo","msg":"./src/app.tsx:14:0-70"}
{"level":50,"time":1748958326246,"pid":22276,"hostname":"desktopRo","msg":"./src/pages/ProductList/index.tsx:13:0-115"}
{"level":50,"time":1748958326246,"pid":22276,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":50,"time":1748958326331,"pid":22276,"hostname":"desktopRo","msg":"[icons] build failed: Error: Build failed with 3 errors:\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/authController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src'\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/authController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\User\\Login'\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/productController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\ProductList'"}
{"level":55,"time":1748958326341,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958326368,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958326641,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 274 ms (501 modules)"}
{"level":30,"time":1748958326642,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748958326647,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958326673,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958326770,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 98 ms (501 modules)"}
{"level":30,"time":1748958326772,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748958325980,"pid":43424,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1748958326387,"pid":43424,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":30,"time":1748958512913,"pid":23336,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1748958513361,"pid":23336,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":55,"time":1748958512979,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958512983,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748958513200,"pid":22276,"hostname":"desktopRo","msg":"./src/app.tsx:14:0-70"}
{"level":50,"time":1748958513201,"pid":22276,"hostname":"desktopRo","msg":"./src/pages/ProductList/index.tsx:13:0-115"}
{"level":50,"time":1748958513201,"pid":22276,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":55,"time":1748958513313,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958513344,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958513560,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 218 ms (501 modules)"}
{"level":30,"time":1748958513562,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748958513567,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958513604,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958513705,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 102 ms (501 modules)"}
{"level":30,"time":1748958513707,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748958678045,"pid":22276,"hostname":"desktopRo","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":55,"time":1748958678189,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958678213,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958678713,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 502 ms (506 modules)"}
{"level":30,"time":1748958678717,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] buildDeps since moduleGraph has changed"}
{"level":20,"time":1748958678718,"pid":22276,"hostname":"desktopRo","msg":"C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/dist/reset.css, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/asyncToGenerator.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/objectSpread2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/regeneratorRuntime.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/duration, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localizedFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localeData, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isMoment, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekOfYear, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekYear, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekday, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/customParseFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/advancedFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrAfter, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrBefore, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/antd-dayjs-webpack-plugin@1.0.6_dayjs@1.11.13/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/typeof.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react/jsx-dev-runtime, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/regenerator-runtime@0.13.11/node_modules/regenerator-runtime/runtime.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.size.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.has.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.delete.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url.can-parse.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.structured-clone.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.self.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.immediate.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.dom-exception.stack.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.add-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.upsert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.emplace.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.to-hex.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.to-base64.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.from-hex.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.from-base64.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.unique-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.to-spliced.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.filter-reject.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.filter-out.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.from-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.replace-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.pattern-match.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.observable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.metadata-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.matcher.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-well-known.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-well-known-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-registered.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-registered-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.async-dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.dedent.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.code-points.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.cooked.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.union.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.union.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.symmetric-difference.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.symmetric-difference.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.join.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-superset-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-superset-of.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-subset-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-subset-of.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-disjoint-from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-disjoint-from.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.intersection.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.intersection.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.difference.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.difference.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.add-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.regexp.escape.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.has-own-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.has-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-own-metadata-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-own-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-metadata-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.delete-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.define-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.promise.try.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.observable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-values.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-entries.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.number.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.number.from-string.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.umulh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.signbit.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.seeded-prng.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.scale.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.radians.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.rad-per-deg.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.isubh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.imulh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.iaddh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.f16round.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.fscale.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.degrees.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.deg-per-rad.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.clamp.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.upsert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.update-or-insert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.update.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.merge.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.map-values.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.map-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.key-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.key-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.includes.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.find-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.emplace.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.raw-json.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.parse.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.is-raw-json.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.to-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.to-array.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.take.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.indexed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.for-each.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.flat-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.drop.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.as-indexed-pairs.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.un-this.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.is-constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.is-callable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.demethodize.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.disposable-stack.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.set-uint8-clamped.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.set-float16.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.get-uint8-clamped.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.get-float16.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.composite-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.composite-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.bigint.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.to-array.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.take.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.indexed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.for-each.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.flat-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.drop.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.async-dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.as-indexed-pairs.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-disposable-stack.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.transfer-to-fixed-length.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.transfer.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.detached.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.unique-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.last-item.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.is-template-object.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-to-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-by-to-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.filter-reject.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.filter-out.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.from-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.suppressed-error.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.with.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.to-sorted.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.to-reversed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.set.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.find-last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.find-last.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.to-well-formed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.replace-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.is-well-formed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.at-alternative.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.regexp.flags.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.reflect.to-string-tag.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.promise.with-resolvers.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.promise.any.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.object.has-own.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.object.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.map.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.with.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-spliced.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-sorted.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-reversed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.reduce-right.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.push.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.find-last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.find-last.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.aggregate-error.cause.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.aggregate-error.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.error.cause.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/umi@4.4.11_@babel+core@7.4._97aef5f84ba3bed6a7b4ab93e4bba605/node_modules/umi/client/client/plugin.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/slicedToArray.js, @ant-design/pro-components, @ant-design/icons, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/TableOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/CrownOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/SmileOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-tw, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-cn, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/pt-br, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ja, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/id, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/fa, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/en, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/bn-bd, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/zh_TW, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/zh_CN, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/pt_BR, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/ja_JP, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/id_ID, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/fa_IR, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/en_US, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/bn_BD, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/react-intl@3.12.1_react@18.3.1/node_modules/react-intl, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/warning@4.0.3/node_modules/warning, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/event-emitter@0.3.5/node_modules/event-emitter, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/objectWithoutProperties.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/@ant-design/pro-components, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/toConsumableArray.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/defineProperty.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ahooksjs+use-request@2.8.15_react@18.3.1/node_modules/@ahooksjs/use-request, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/axios@0.27.2/node_modules/axios, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/fast-deep-equal@3.1.3/node_modules/fast-deep-equal/index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/classCallCheck.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/createClass.js, swagger-ui-dist/swagger-ui.css, swagger-ui-dist, antd-style, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react-dom, querystring, classnames, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined, @ant-design/pro-table, @ant-design/pro-layout"}
{"level":50,"time":1748958679391,"pid":22276,"hostname":"desktopRo","msg":"Can not resolve dependence : '\u001b[31m@ant-design/pro-table\u001b[39m', please install it"}
{"level":50,"time":1748958679398,"pid":22276,"hostname":"desktopRo","err":{"type":"AssertionError","message":"dependence not found: @ant-design/pro-table","stack":"AssertionError [ERR_ASSERTION]: dependence not found: @ant-design/pro-table\n    at Dep.buildExposeContent (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+mfsu@4.4.11\\node_modules\\@umijs\\mfsu\\dist\\dep\\dep.js:90:31)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DepBuilder.writeMFFiles (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+mfsu@4.4.11\\node_modules\\@umijs\\mfsu\\dist\\depBuilder\\depBuilder.js:159:23)\n    at async DepBuilder.build (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+mfsu@4.4.11\\node_modules\\@umijs\\mfsu\\dist\\depBuilder\\depBuilder.js:137:7)\n    at async MFSU.buildDeps (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+mfsu@4.4.11\\node_modules\\@umijs\\mfsu\\dist\\mfsu\\mfsu.js:227:7)","generatedMessage":false,"code":"ERR_ASSERTION","actual":null,"expected":true,"operator":"=="},"msg":"dependence not found: @ant-design/pro-table"}
{"level":55,"time":1748958711792,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958711820,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958711995,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 176 ms (506 modules)"}
{"level":30,"time":1748958711997,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748958724436,"pid":22276,"hostname":"desktopRo","msg":"config routes changed, regenerate tmp files..."}
{"level":55,"time":1748958724607,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958724630,"pid":22276,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958724900,"pid":22276,"hostname":"desktopRo","msg":"[Webpack] Compiled in 270 ms (506 modules)"}
{"level":30,"time":1748958724902,"pid":22276,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748958791720,"pid":66900,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 编写 src/loading.(jsx|tsx) 可以自定义页面的加载动画。\u001b[39m"}
{"level":30,"time":1748958791722,"pid":66900,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748958792626,"pid":66900,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748958795256,"pid":66900,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748958797046,"pid":66900,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748958797129,"pid":66900,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748958800106,"pid":66900,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3057 ms (520 modules)"}
{"level":30,"time":1748958800112,"pid":66900,"hostname":"desktopRo","msg":"[MFSU] buildDeps since moduleGraph has changed"}
{"level":20,"time":1748958800112,"pid":66900,"hostname":"desktopRo","msg":"C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@umijs+renderer-react@4.4.1_5d37cdc93ae4c557f74cbc834d273583/node_modules/@umijs/renderer-react, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/dist/reset.css, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/asyncToGenerator.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/objectSpread2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/regeneratorRuntime.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/duration, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localizedFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/localeData, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isMoment, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekOfYear, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekYear, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/weekday, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/customParseFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/advancedFormat, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrAfter, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/plugin/isSameOrBefore, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/antd-dayjs-webpack-plugin@1.0.6_dayjs@1.11.13/node_modules/antd-dayjs-webpack-plugin/src/antd-plugin.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/typeof.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react/jsx-dev-runtime, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/regenerator-runtime@0.13.11/node_modules/regenerator-runtime/runtime.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.size.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.has.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url-search-params.delete.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.url.can-parse.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.structured-clone.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.self.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.immediate.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/web.dom-exception.stack.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-set.add-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.upsert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.emplace.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.weak-map.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.to-hex.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.to-base64.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.from-hex.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.uint8-array.from-base64.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.unique-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.to-spliced.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.filter-reject.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.filter-out.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.typed-array.from-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.replace-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.pattern-match.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.observable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.metadata-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.matcher.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-well-known.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-well-known-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-registered.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.is-registered-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.symbol.async-dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.dedent.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.code-points.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.cooked.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.string.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.union.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.union.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.symmetric-difference.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.symmetric-difference.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.join.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-superset-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-superset-of.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-subset-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-subset-of.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-disjoint-from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.is-disjoint-from.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.intersection.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.intersection.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.difference.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.difference.v2.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.set.add-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.regexp.escape.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.has-own-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.has-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-own-metadata-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-own-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-metadata-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.get-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.delete-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.reflect.define-metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.promise.try.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.observable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-values.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.object.iterate-entries.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.number.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.number.from-string.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.umulh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.signbit.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.seeded-prng.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.scale.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.radians.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.rad-per-deg.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.isubh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.imulh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.iaddh.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.f16round.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.fscale.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.degrees.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.deg-per-rad.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.math.clamp.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.upsert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.update-or-insert.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.update.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.merge.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.map-values.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.map-keys.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.key-of.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.key-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.includes.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.find-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.emplace.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.map.delete-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.raw-json.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.parse.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.json.is-raw-json.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.to-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.to-array.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.take.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.indexed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.for-each.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.flat-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.drop.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.as-indexed-pairs.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.iterator.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.un-this.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.metadata.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.is-constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.is-callable.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.function.demethodize.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.disposable-stack.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.set-uint8-clamped.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.set-float16.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.get-uint8-clamped.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.data-view.get-float16.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.composite-symbol.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.composite-key.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.bigint.range.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.to-array.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.take.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.some.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.indexed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.from.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.for-each.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.flat-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.find.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.filter.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.every.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.drop.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.async-dispose.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.as-indexed-pairs.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-iterator.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.async-disposable-stack.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.transfer-to-fixed-length.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.transfer.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array-buffer.detached.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.unique-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.last-item.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.is-template-object.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-to-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-by-to-map.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.group.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.filter-reject.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.filter-out.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.array.from-async.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/esnext.suppressed-error.constructor.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.with.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.to-sorted.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.to-reversed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.set.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.find-last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.find-last.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.typed-array.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.to-well-formed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.replace-all.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.is-well-formed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.string.at-alternative.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.regexp.flags.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.reflect.to-string-tag.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.promise.with-resolvers.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.promise.any.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.object.has-own.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.object.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.map.group-by.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.with.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-spliced.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-sorted.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.to-reversed.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.reduce-right.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.reduce.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.push.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.find-last-index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.find-last.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.array.at.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.aggregate-error.cause.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.aggregate-error.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/core-js@3.34.0/node_modules/core-js/modules/es.error.cause.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/umi@4.4.11_@babel+core@7.4._97aef5f84ba3bed6a7b4ab93e4bba605/node_modules/umi/client/client/plugin.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/slicedToArray.js, @ant-design/pro-components, @ant-design/icons, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/TableOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/CrownOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/SmileOutlined, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-tw, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/zh-cn, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/pt-br, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/ja, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/id, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/fa, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/en, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/locale/bn-bd, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/zh_TW, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/zh_CN, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/pt_BR, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/ja_JP, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/id_ID, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/fa_IR, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/en_US, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/antd/es/locale/bn_BD, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/react-intl@3.12.1_react@18.3.1/node_modules/react-intl, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/warning@4.0.3/node_modules/warning, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/event-emitter@0.3.5/node_modules/event-emitter, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/objectWithoutProperties.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/@ant-design/pro-components, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/createForOfIteratorHelper.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/toConsumableArray.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/defineProperty.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ahooksjs+use-request@2.8.15_react@18.3.1/node_modules/@ahooksjs/use-request, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/axios@0.27.2/node_modules/axios, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/fast-deep-equal@3.1.3/node_modules/fast-deep-equal/index.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/classCallCheck.js, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@babel+runtime@7.23.6/node_modules/@babel/runtime/helpers/createClass.js, swagger-ui-dist/swagger-ui.css, swagger-ui-dist, antd-style, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/react-dom, querystring, classnames, C:/Users/<USER>/Desktop/mall/mall-admin/node_modules/.pnpm/@ant-design+icons@4.8.3_rea_19faa62653f401826ad5c42b19a83e78/node_modules/@ant-design/icons/es/icons/ShoppingCartOutlined"}
{"level":55,"time":1748958800232,"pid":66900,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958800259,"pid":66900,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958800357,"pid":66900,"hostname":"desktopRo","msg":"[Webpack] Compiled in 99 ms (506 modules)"}
{"level":32,"time":1748958807245,"pid":66900,"hostname":"desktopRo","msg":"[Webpack] Compiled in 6583 ms (5688 modules)"}
{"level":30,"time":1748958807248,"pid":66900,"hostname":"desktopRo","msg":"[MFSU] write cache"}
{"level":30,"time":1748958807249,"pid":66900,"hostname":"desktopRo","msg":"[MFSU] buildDepsAgain"}
{"level":30,"time":1748958807250,"pid":66900,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748958926780,"pid":66900,"hostname":"desktopRo","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":55,"time":1748958926993,"pid":66900,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748958927035,"pid":66900,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748958927494,"pid":66900,"hostname":"desktopRo","msg":"[Webpack] Compiled in 459 ms (506 modules)"}
{"level":30,"time":1748958927497,"pid":66900,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748959008111,"pid":66900,"hostname":"desktopRo","msg":"config routes changed, regenerate tmp files..."}
{"level":30,"time":1748959045502,"pid":35500,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1748959045504,"pid":35500,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748959046262,"pid":35500,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748959047906,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748959048551,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748959048586,"pid":35500,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748959051008,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2455 ms (520 modules)"}
{"level":30,"time":1748959051011,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748959051101,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748959051102,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748959051213,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 111 ms (506 modules)"}
{"level":30,"time":1748959051215,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748959051256,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748959051258,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748959051347,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 90 ms (506 modules)"}
{"level":30,"time":1748959051349,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748959069553,"pid":35500,"hostname":"desktopRo","msg":"config routes changed, regenerate tmp files..."}
{"level":55,"time":1748959069675,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748959069698,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748959069981,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 284 ms (506 modules)"}
{"level":30,"time":1748959069983,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748959076338,"pid":35500,"hostname":"desktopRo","msg":"config routes changed, regenerate tmp files..."}
{"level":55,"time":1748959076462,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748959076485,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748959076682,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 198 ms (506 modules)"}
{"level":30,"time":1748959076685,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748959121669,"pid":35500,"hostname":"desktopRo","msg":"config routes, routes changed, regenerate tmp files..."}
{"level":55,"time":1748959121828,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748959121851,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748959122061,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 211 ms (506 modules)"}
{"level":30,"time":1748959122063,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748959795974,"pid":20284,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果要支持低版本浏览器，可尝试新出的 legacy 配置项，详见 https://umijs.org/blog/legacy-browser\u001b[39m"}
{"level":30,"time":1748959795975,"pid":20284,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748959796820,"pid":20284,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748959812855,"pid":20284,"hostname":"desktopRo","msg":"Memory Usage: 669.59 MB (RSS: 994.45 MB)"}
{"level":60,"time":1748959812864,"pid":20284,"hostname":"desktopRo","err":{"type":"Error","message":"ERROR in ./src/pages/OrderList/index.tsx 3:0-55\nModule not found: Error: Can't resolve '@ant-design/pro-layout' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\OrderList'\n @ ./src/.umi-production/core/route.tsx\n @ ./src/.umi-production/umi.ts 12:0-41 29:17-26\n\nERROR in ./src/pages/OrderList/index.tsx 4:0-45\nModule not found: Error: Can't resolve '@ant-design/pro-table' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\OrderList'\n @ ./src/.umi-production/core/route.tsx\n @ ./src/.umi-production/umi.ts 12:0-41 29:17-26\n\nwebpack compiled with 2 errors","stack":"Error: ERROR in ./src/pages/OrderList/index.tsx 3:0-55\nModule not found: Error: Can't resolve '@ant-design/pro-layout' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\OrderList'\n @ ./src/.umi-production/core/route.tsx\n @ ./src/.umi-production/umi.ts 12:0-41 29:17-26\n\nERROR in ./src/pages/OrderList/index.tsx 4:0-45\nModule not found: Error: Can't resolve '@ant-design/pro-table' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\OrderList'\n @ ./src/.umi-production/core/route.tsx\n @ ./src/.umi-production/umi.ts 12:0-41 29:17-26\n\nwebpack compiled with 2 errors\n    at handler (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-webpack@4.4._67445fed6bb8e5a34f63274d3481fab8\\node_modules\\@umijs\\bundler-webpack\\dist\\build.js:82:79)\n    at finalCallback (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-webpack@4.4._67445fed6bb8e5a34f63274d3481fab8\\node_modules\\@umijs\\bundler-webpack\\compiled\\webpack\\index.js:62945:32)\n    at C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-webpack@4.4._67445fed6bb8e5a34f63274d3481fab8\\node_modules\\@umijs\\bundler-webpack\\compiled\\webpack\\index.js:62962:13\n    at Hook.eval [as callAsync] (eval at create (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-utils@4.4.11\\node_modules\\@umijs\\bundler-utils\\compiled\\tapable\\index.js:1:7682), <anonymous>:35:1)\n    at Hook.CALL_ASYNC_DELEGATE [as _callAsync] (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-utils@4.4.11\\node_modules\\@umijs\\bundler-utils\\compiled\\tapable\\index.js:1:4851)\n    at onCompiled (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-webpack@4.4._67445fed6bb8e5a34f63274d3481fab8\\node_modules\\@umijs\\bundler-webpack\\compiled\\webpack\\index.js:62960:21)\n    at C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-webpack@4.4._67445fed6bb8e5a34f63274d3481fab8\\node_modules\\@umijs\\bundler-webpack\\compiled\\webpack\\index.js:63704:17\n    at _next0 (eval at create (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-utils@4.4.11\\node_modules\\@umijs\\bundler-utils\\compiled\\tapable\\index.js:1:7682), <anonymous>:16:1)\n    at eval (eval at create (C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.pnpm\\@umijs+bundler-utils@4.4.11\\node_modules\\@umijs\\bundler-utils\\compiled\\tapable\\index.js:1:7682), <anonymous>:26:1)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)"},"msg":"ERROR in ./src/pages/OrderList/index.tsx 3:0-55\nModule not found: Error: Can't resolve '@ant-design/pro-layout' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\OrderList'\n @ ./src/.umi-production/core/route.tsx\n @ ./src/.umi-production/umi.ts 12:0-41 29:17-26\n\nERROR in ./src/pages/OrderList/index.tsx 4:0-45\nModule not found: Error: Can't resolve '@ant-design/pro-table' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\OrderList'\n @ ./src/.umi-production/core/route.tsx\n @ ./src/.umi-production/umi.ts 12:0-41 29:17-26\n\nwebpack compiled with 2 errors"}
{"level":60,"time":1748959813225,"pid":20284,"hostname":"desktopRo","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1748959813240,"pid":20284,"hostname":"desktopRo","msg":"C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1748959813256,"pid":20284,"hostname":"desktopRo","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":55,"time":1748959949296,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748959949331,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748959949489,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 159 ms (506 modules)"}
{"level":30,"time":1748959949491,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748959959820,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748959959847,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748959960085,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 239 ms (504 modules)"}
{"level":30,"time":1748959960087,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748959970817,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748959970845,"pid":35500,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748959971038,"pid":35500,"hostname":"desktopRo","msg":"[Webpack] Compiled in 194 ms (504 modules)"}
{"level":30,"time":1748959971040,"pid":35500,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748959986059,"pid":69640,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1748959986060,"pid":69640,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748959986853,"pid":69640,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748960003153,"pid":69640,"hostname":"desktopRo","msg":"Memory Usage: 677.59 MB (RSS: 983.94 MB)"}
{"level":30,"time":1748960005953,"pid":69640,"hostname":"desktopRo","msg":"File sizes after gzip:\n"}
{"level":32,"time":1748960006060,"pid":69640,"hostname":"desktopRo","msg":"Build index.html"}
{"level":30,"time":1748960022705,"pid":22420,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1748960022707,"pid":22420,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748960023468,"pid":22420,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748960025610,"pid":22420,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748960026262,"pid":22420,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748960026292,"pid":22420,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748960032409,"pid":22420,"hostname":"desktopRo","msg":"[Webpack] Compiled in 6144 ms (530 modules)"}
{"level":30,"time":1748960032411,"pid":22420,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960032558,"pid":22420,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960032560,"pid":22420,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960032679,"pid":22420,"hostname":"desktopRo","msg":"[Webpack] Compiled in 120 ms (504 modules)"}
{"level":30,"time":1748960032681,"pid":22420,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748960163753,"pid":30460,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1748960165095,"pid":30460,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":55,"time":1748960164626,"pid":22420,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960164632,"pid":22420,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1748960164897,"pid":22420,"hostname":"desktopRo","msg":"./src/app.tsx:14:0-70"}
{"level":50,"time":1748960164898,"pid":22420,"hostname":"desktopRo","msg":"./src/pages/OrderList/index.tsx:11:0-79"}
{"level":50,"time":1748960164898,"pid":22420,"hostname":"desktopRo","msg":"./src/pages/ProductList/index.tsx:13:0-115"}
{"level":50,"time":1748960164899,"pid":22420,"hostname":"desktopRo","msg":"./src/pages/User/Login/index.tsx:13:0-58"}
{"level":55,"time":1748960165030,"pid":22420,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":50,"time":1748960165050,"pid":22420,"hostname":"desktopRo","msg":"[icons] build failed: Error: Build failed with 4 errors:\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/authController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src'\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/authController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\User\\Login'\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/orderController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\OrderList'\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/services/swagger/productController' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\pages\\ProductList'"}
{"level":20,"time":1748960165054,"pid":22420,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960165442,"pid":22420,"hostname":"desktopRo","msg":"[Webpack] Compiled in 388 ms (504 modules)"}
{"level":30,"time":1748960165444,"pid":22420,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960165451,"pid":22420,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960165484,"pid":22420,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960165655,"pid":22420,"hostname":"desktopRo","msg":"[Webpack] Compiled in 171 ms (504 modules)"}
{"level":30,"time":1748960165657,"pid":22420,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748960432462,"pid":65684,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints\u001b[39m"}
{"level":30,"time":1748960432464,"pid":65684,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748960433236,"pid":65684,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748960434892,"pid":65684,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748960435534,"pid":65684,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748960435569,"pid":65684,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748960437890,"pid":65684,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2354 ms (518 modules)"}
{"level":30,"time":1748960437892,"pid":65684,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960437983,"pid":65684,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960437984,"pid":65684,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960438103,"pid":65684,"hostname":"desktopRo","msg":"[Webpack] Compiled in 118 ms (504 modules)"}
{"level":30,"time":1748960438105,"pid":65684,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960438145,"pid":65684,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960438146,"pid":65684,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960438221,"pid":65684,"hostname":"desktopRo","msg":"[Webpack] Compiled in 75 ms (504 modules)"}
{"level":30,"time":1748960438223,"pid":65684,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748960545274,"pid":44556,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 请求加载态、数据管理、避免竟态问题，用 react-query 帮你全部解决，详见 https://umijs.org/docs/max/react-query\u001b[39m"}
{"level":30,"time":1748960545276,"pid":44556,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748960546004,"pid":44556,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748960547620,"pid":44556,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748960548273,"pid":44556,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748960548302,"pid":44556,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748960550667,"pid":44556,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2392 ms (518 modules)"}
{"level":30,"time":1748960550669,"pid":44556,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960550754,"pid":44556,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960550755,"pid":44556,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960550887,"pid":44556,"hostname":"desktopRo","msg":"[Webpack] Compiled in 132 ms (504 modules)"}
{"level":30,"time":1748960550889,"pid":44556,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960550921,"pid":44556,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960550924,"pid":44556,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960551028,"pid":44556,"hostname":"desktopRo","msg":"[Webpack] Compiled in 105 ms (504 modules)"}
{"level":30,"time":1748960551030,"pid":44556,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1748960708680,"pid":66132,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 请求加载态、数据管理、避免竟态问题，用 react-query 帮你全部解决，详见 https://umijs.org/docs/max/react-query\u001b[39m"}
{"level":30,"time":1748960708682,"pid":66132,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1748960709524,"pid":66132,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1748960711341,"pid":66132,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1748960711944,"pid":66132,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1748960711975,"pid":66132,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1748960715572,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3624 ms (518 modules)"}
{"level":30,"time":1748960715574,"pid":66132,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960715701,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960715702,"pid":66132,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960715867,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiled in 165 ms (504 modules)"}
{"level":30,"time":1748960715868,"pid":66132,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960715904,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960715907,"pid":66132,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960716077,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiled in 173 ms (504 modules)"}
{"level":30,"time":1748960716079,"pid":66132,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1748960867697,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960867733,"pid":66132,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960867856,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiled in 124 ms (504 modules)"}
{"level":30,"time":1748960867858,"pid":66132,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1748960899404,"pid":66132,"hostname":"desktopRo","msg":"config routes changed, regenerate tmp files..."}
{"level":55,"time":1748960899527,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1748960899550,"pid":66132,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1748960899848,"pid":66132,"hostname":"desktopRo","msg":"[Webpack] Compiled in 298 ms (504 modules)"}
{"level":30,"time":1748960899850,"pid":66132,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1749046574330,"pid":43732,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1749046574331,"pid":43732,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749046576522,"pid":43732,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749046579821,"pid":43732,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749046581097,"pid":43732,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749046581142,"pid":43732,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749046584944,"pid":43732,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3844 ms (518 modules)"}
{"level":30,"time":1749046584948,"pid":43732,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749046585115,"pid":43732,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749046585116,"pid":43732,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749046585398,"pid":43732,"hostname":"desktopRo","msg":"[Webpack] Compiled in 283 ms (504 modules)"}
{"level":30,"time":1749046585400,"pid":43732,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749046585443,"pid":43732,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749046585445,"pid":43732,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749046585548,"pid":43732,"hostname":"desktopRo","msg":"[Webpack] Compiled in 105 ms (504 modules)"}
{"level":30,"time":1749046585550,"pid":43732,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1749046628204,"pid":34440,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 编写 src/loading.(jsx|tsx) 可以自定义页面的加载动画。\u001b[39m"}
{"level":30,"time":1749046628207,"pid":34440,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749046629000,"pid":34440,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749046631156,"pid":34440,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749046631896,"pid":34440,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749046631927,"pid":34440,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749046634252,"pid":34440,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2353 ms (518 modules)"}
{"level":30,"time":1749046634255,"pid":34440,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749046634340,"pid":34440,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749046634341,"pid":34440,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749046634457,"pid":34440,"hostname":"desktopRo","msg":"[Webpack] Compiled in 117 ms (504 modules)"}
{"level":30,"time":1749046634459,"pid":34440,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749046634501,"pid":34440,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749046634502,"pid":34440,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749046634575,"pid":34440,"hostname":"desktopRo","msg":"[Webpack] Compiled in 74 ms (504 modules)"}
{"level":30,"time":1749046634577,"pid":34440,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1749126331648,"pid":16852,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1749126332290,"pid":16852,"hostname":"desktopRo","msg":"\u001b[32m[plugin: @umijs/max-plugin-openapi]\u001b[39m"}
{"level":30,"time":1749126742351,"pid":40424,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] COMPRESS=none max build 可以关闭项目构建时的代码压缩功能, 方便调试项目的构建产物。\u001b[39m"}
{"level":30,"time":1749126742353,"pid":40424,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749126743104,"pid":40424,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749126744601,"pid":40424,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749126745383,"pid":40424,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749126745414,"pid":40424,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749126747892,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2506 ms (518 modules)"}
{"level":30,"time":1749126747894,"pid":40424,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749126747989,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749126747991,"pid":40424,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749126748104,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiled in 113 ms (504 modules)"}
{"level":30,"time":1749126748105,"pid":40424,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749126748147,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749126748148,"pid":40424,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749126748217,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiled in 69 ms (504 modules)"}
{"level":30,"time":1749126748218,"pid":40424,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749126858778,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749126858815,"pid":40424,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749126859138,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiled in 330 ms (504 modules)"}
{"level":30,"time":1749126859142,"pid":40424,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749127684681,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749127684708,"pid":40424,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749127684993,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiled in 286 ms (504 modules)"}
{"level":30,"time":1749127684996,"pid":40424,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1749127883000,"pid":40424,"hostname":"desktopRo","msg":"config routes changed, regenerate tmp files..."}
{"level":55,"time":1749127883164,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749127883187,"pid":40424,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749127883564,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiled in 378 ms (505 modules)"}
{"level":30,"time":1749127883567,"pid":40424,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749127895330,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749127895355,"pid":40424,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749127895617,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiled in 264 ms (505 modules)"}
{"level":30,"time":1749127895619,"pid":40424,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749128100908,"pid":40424,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749128100910,"pid":40424,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":50,"time":1749128101193,"pid":40424,"hostname":"desktopRo","msg":"./src/.umi/core/route.tsx:158:23-117"}
{"level":50,"time":1749128101293,"pid":40424,"hostname":"desktopRo","msg":"[icons] build failed: Error: Build failed with 1 error:\nnode_modules/.pnpm/enhanced-resolve@5.9.3/node_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'C:/Users/<USER>/Desktop/mall/mall-admin/src/pages/Order/Products/index.tsx' in 'C:\\Users\\<USER>\\Desktop\\mall\\mall-admin\\src\\.umi\\core'"}
{"level":30,"time":1749128373524,"pid":36096,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] father 4 正式发布了，详见 https://zhuanlan.zhihu.com/p/558192063\u001b[39m"}
{"level":30,"time":1749128373526,"pid":36096,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749128374386,"pid":36096,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749128376054,"pid":36096,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749128376773,"pid":36096,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749128376804,"pid":36096,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749128379263,"pid":36096,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2487 ms (518 modules)"}
{"level":30,"time":1749128379265,"pid":36096,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749128379356,"pid":36096,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749128379358,"pid":36096,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749128379489,"pid":36096,"hostname":"desktopRo","msg":"[Webpack] Compiled in 131 ms (504 modules)"}
{"level":30,"time":1749128379491,"pid":36096,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749128379534,"pid":36096,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749128379536,"pid":36096,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749128379614,"pid":36096,"hostname":"desktopRo","msg":"[Webpack] Compiled in 79 ms (504 modules)"}
{"level":30,"time":1749128379616,"pid":36096,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749128455046,"pid":36096,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749128455086,"pid":36096,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749128455363,"pid":36096,"hostname":"desktopRo","msg":"[Webpack] Compiled in 277 ms (504 modules)"}
{"level":30,"time":1749128455365,"pid":36096,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1749130593093,"pid":35656,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints\u001b[39m"}
{"level":30,"time":1749130593094,"pid":35656,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749130593963,"pid":35656,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749130595054,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749130595704,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749130595736,"pid":35656,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749130598223,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2517 ms (518 modules)"}
{"level":30,"time":1749130598225,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749130598324,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749130598355,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749130598457,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 104 ms (504 modules)"}
{"level":30,"time":1749130598459,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749130795689,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749130795728,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749130796215,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 484 ms (504 modules)"}
{"level":30,"time":1749130796232,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749130837144,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749130837180,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749130837488,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 309 ms (504 modules)"}
{"level":30,"time":1749130837492,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749130956895,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749130956920,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749130957437,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 509 ms (504 modules)"}
{"level":30,"time":1749130957444,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1749131671240,"pid":35656,"hostname":"desktopRo","msg":"config routes changed, regenerate tmp files..."}
{"level":55,"time":1749131671752,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749131671791,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749131673803,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 1984 ms (505 modules)"}
{"level":30,"time":1749131673846,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749131761248,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749131761272,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749131761556,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 285 ms (505 modules)"}
{"level":30,"time":1749131761559,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749131914644,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749131914670,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749131915210,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 527 ms (505 modules)"}
{"level":30,"time":1749131915226,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749132373741,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749132373766,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749132373992,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 226 ms (505 modules)"}
{"level":30,"time":1749132373994,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749132416725,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749132416752,"pid":35656,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749132416999,"pid":35656,"hostname":"desktopRo","msg":"[Webpack] Compiled in 249 ms (505 modules)"}
{"level":30,"time":1749132417001,"pid":35656,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1749179387241,"pid":44272,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 如果想点击组件跳转至编辑器源码位置，可尝试新出的 clickToComponent 配置项，详见 https://umijs.org/docs/api/config#clicktocomponent\u001b[39m"}
{"level":30,"time":1749179387243,"pid":44272,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749179388005,"pid":44272,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749179389351,"pid":44272,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749179390329,"pid":44272,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749179390358,"pid":44272,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749179392902,"pid":44272,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2572 ms (519 modules)"}
{"level":30,"time":1749179392905,"pid":44272,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749179392996,"pid":44272,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749179393001,"pid":44272,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749179393118,"pid":44272,"hostname":"desktopRo","msg":"[Webpack] Compiled in 121 ms (505 modules)"}
{"level":30,"time":1749179393120,"pid":44272,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749179393166,"pid":44272,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749179393168,"pid":44272,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749179393254,"pid":44272,"hostname":"desktopRo","msg":"[Webpack] Compiled in 87 ms (505 modules)"}
{"level":30,"time":1749179393256,"pid":44272,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":32,"time":1749179516654,"pid":44272,"hostname":"desktopRo","msg":"config routes changed, regenerate tmp files..."}
{"level":55,"time":1749179517153,"pid":44272,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749179517179,"pid":44272,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749179518073,"pid":44272,"hostname":"desktopRo","msg":"[Webpack] Compiled in 895 ms (505 modules)"}
{"level":30,"time":1749179518074,"pid":44272,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1749215248767,"pid":57556,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] COMPRESS=none max build 可以关闭项目构建时的代码压缩功能, 方便调试项目的构建产物。\u001b[39m"}
{"level":30,"time":1749215248768,"pid":57556,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749215249649,"pid":57556,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749215251136,"pid":57556,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749215252071,"pid":57556,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749215252105,"pid":57556,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749215255295,"pid":57556,"hostname":"desktopRo","msg":"[Webpack] Compiled in 3223 ms (519 modules)"}
{"level":30,"time":1749215255301,"pid":57556,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749215255424,"pid":57556,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749215255425,"pid":57556,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749215255517,"pid":57556,"hostname":"desktopRo","msg":"[Webpack] Compiled in 92 ms (505 modules)"}
{"level":30,"time":1749215255518,"pid":57556,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1749476497868,"pid":43228,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1749476497869,"pid":43228,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749476498873,"pid":43228,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749476502326,"pid":43228,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749476503182,"pid":43228,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749476503217,"pid":43228,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749476505609,"pid":43228,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2425 ms (519 modules)"}
{"level":30,"time":1749476505612,"pid":43228,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749476505700,"pid":43228,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749476505701,"pid":43228,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749476505831,"pid":43228,"hostname":"desktopRo","msg":"[Webpack] Compiled in 131 ms (505 modules)"}
{"level":30,"time":1749476505833,"pid":43228,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749476505878,"pid":43228,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749476505880,"pid":43228,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749476505977,"pid":43228,"hostname":"desktopRo","msg":"[Webpack] Compiled in 98 ms (505 modules)"}
{"level":30,"time":1749476505979,"pid":43228,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":30,"time":1749477134837,"pid":44880,"hostname":"desktopRo","msg":"\u001b[33m[你知道吗？] 全局样式、全局脚本写在哪里？创建 src/global.(ts|css) 轻松解决，详见 https://umijs.org/docs/guides/directory-structure#globaljtsx\u001b[39m"}
{"level":30,"time":1749477134840,"pid":44880,"hostname":"desktopRo","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1749477136151,"pid":44880,"hostname":"desktopRo","msg":"Preparing..."}
{"level":30,"time":1749477137436,"pid":44880,"hostname":"desktopRo","msg":"[MFSU] restore cache"}
{"level":20,"time":1749477138061,"pid":44880,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":31,"time":1749477138090,"pid":44880,"hostname":"desktopRo","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://**********:8000\u001b[39m                 \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1749477140812,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiled in 2749 ms (519 modules)"}
{"level":30,"time":1749477140814,"pid":44880,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749477140919,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749477140923,"pid":44880,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749477141057,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiled in 137 ms (505 modules)"}
{"level":30,"time":1749477141059,"pid":44880,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749477141099,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749477141101,"pid":44880,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749477141197,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiled in 96 ms (505 modules)"}
{"level":30,"time":1749477141198,"pid":44880,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749477570879,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749477570908,"pid":44880,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749477571306,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiled in 400 ms (505 modules)"}
{"level":30,"time":1749477571308,"pid":44880,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749477590283,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749477590310,"pid":44880,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749477590558,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiled in 249 ms (505 modules)"}
{"level":30,"time":1749477590559,"pid":44880,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
{"level":55,"time":1749477603835,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiling..."}
{"level":20,"time":1749477603870,"pid":44880,"hostname":"desktopRo","msg":"webpack watched change"}
{"level":32,"time":1749477604159,"pid":44880,"hostname":"desktopRo","msg":"[Webpack] Compiled in 290 ms (505 modules)"}
{"level":30,"time":1749477604161,"pid":44880,"hostname":"desktopRo","msg":"[MFSU] skip buildDeps"}
