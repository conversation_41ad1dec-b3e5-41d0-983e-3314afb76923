-- 檢查數據庫結構更新腳本
-- 請在MySQL中執行此腳本來驗證表結構

USE mall;

-- 1. 檢查所有表是否存在
SELECT 'Tables Check' as 'Check Type';
SHOW TABLES;

-- 2. 檢查users表結構（應該沒有role字段了）
SELECT 'Users Table Structure' as 'Check Type';
DESCRIBE users;

-- 3. 檢查新的roles表
SELECT 'Roles Table Structure' as 'Check Type';
DESCRIBE roles;

-- 4. 檢查user_roles關聯表
SELECT 'User Roles Table Structure' as 'Check Type';
DESCRIBE user_roles;

-- 5. 檢查order_statuses表
SELECT 'Order Statuses Table Structure' as 'Check Type';
DESCRIBE order_statuses;

-- 6. 檢查orders表（應該有status_id外鍵）
SELECT 'Orders Table Structure' as 'Check Type';
DESCRIBE orders;

-- 7. 檢查外鍵約束
SELECT 'Foreign Key Constraints' as 'Check Type';
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM 
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE 
    REFERENCED_TABLE_SCHEMA = 'mall' 
    AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 8. 檢查索引
SELECT 'Indexes Check' as 'Check Type';
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM 
    INFORMATION_SCHEMA.STATISTICS 
WHERE 
    TABLE_SCHEMA = 'mall'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 9. 檢查現有數據
SELECT 'Existing Data Check' as 'Check Type';
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'products' as table_name, COUNT(*) as record_count FROM products
UNION ALL
SELECT 'categories' as table_name, COUNT(*) as record_count FROM categories
UNION ALL
SELECT 'roles' as table_name, COUNT(*) as record_count FROM roles
UNION ALL
SELECT 'order_statuses' as table_name, COUNT(*) as record_count FROM order_statuses;

-- 10. 檢查是否需要初始化數據
SELECT 'Roles Data' as 'Check Type';
SELECT * FROM roles;

SELECT 'Order Statuses Data' as 'Check Type';
SELECT * FROM order_statuses;
