# Mall 電商系統

一個完整的電商系統，包含用戶購物端、管理後台和後端API服務。

## 項目結構

```
mall/
├── ecommerce-backend/     # Spring Boot 後端服務
├── mall-client/          # Next.js 用戶購物端
├── mall-admin/           # 管理後台（計劃中）
└── docs/                 # 項目文檔
    └── progress/         # 開發進度文檔
```

## 技術棧

### 後端 (ecommerce-backend)
- **框架**: Spring Boot 3.x
- **數據庫**: MySQL 8.0
- **ORM**: MyBatis
- **認證**: JWT + Spring Security
- **構建工具**: Maven
- **部署**: Docker

### 前端 (mall-client)
- **框架**: Next.js 15
- **語言**: TypeScript
- **樣式**: Tailwind CSS
- **狀態管理**: Zustand
- **UI組件**: Heroicons
- **HTTP客戶端**: Fetch API

### 管理後台 (mall-admin)
- **框架**: Ant Design Pro + UmiJS (計劃中)
- **狀態管理**: Dva (計劃中)

## 功能特性

### ✅ 已完成功能
- **商品展示模塊**: 商品列表、詳情、搜索、分類
- **購物車系統**: 添加商品、數量管理、價格計算
- **訂單系統**: 訂單創建、狀態管理、地址管理
- **用戶認證系統**: 註冊、登錄、JWT認證
- **訂單發貨功能**: 發貨管理、物流跟踪、狀態更新

### 🚧 開發中功能
- 用戶個人中心
- 支付系統集成

### 📋 計劃功能
- 管理後台完善
- 數據統計分析
- 性能優化
- 部署上線

## 快速開始

### 環境要求
- Node.js 18+
- Java 17+
- MySQL 8.0+
- Maven 3.6+

### 後端啟動

1. 進入後端目錄
```bash
cd ecommerce-backend
```

2. 配置數據庫
```bash
# 創建數據庫
mysql -u root -p
CREATE DATABASE ecommerce_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 修改配置文件
```bash
# 編輯 src/main/resources/application.yml
# 配置數據庫連接信息
```

4. 啟動應用
```bash
mvn spring-boot:run
```

後端服務將在 http://localhost:8080 啟動

### 前端啟動

1. 進入前端目錄
```bash
cd mall-client
```

2. 安裝依賴
```bash
npm install
```

3. 啟動開發服務器
```bash
npm run dev
```

前端應用將在 http://localhost:3000 啟動

## 數據庫設計

### 核心表結構
- `users` - 用戶表
- `user_roles` - 用戶角色關聯表
- `roles` - 角色表
- `products` - 商品表
- `categories` - 分類表
- `orders` - 訂單表
- `order_items` - 訂單項表
- `order_statuses` - 訂單狀態表
- `shipping_info` - 發貨信息表
- `addresses` - 地址表

## API文檔

### 認證相關
- `POST /api/auth/register` - 用戶註冊
- `POST /api/auth/login` - 用戶登錄
- `POST /api/auth/refresh` - 刷新Token

### 商品相關
- `GET /api/products` - 獲取商品列表
- `GET /api/products/{id}` - 獲取商品詳情
- `GET /api/categories` - 獲取分類列表

### 訂單相關
- `POST /api/orders` - 創建訂單
- `GET /api/orders/{id}` - 獲取訂單詳情
- `GET /api/orders/user/{userId}` - 獲取用戶訂單
- `PATCH /api/orders/{id}/status` - 更新訂單狀態

### 發貨相關
- `POST /api/orders/{id}/ship` - 發貨訂單
- `GET /api/orders/{id}/shipping` - 獲取發貨信息
- `PUT /api/orders/{id}/shipping` - 更新發貨信息
- `GET /api/orders/ready-to-ship` - 獲取待發貨訂單

## 開發進度

詳細的開發進度請查看 [docs/progress/](./docs/progress/) 目錄下的文檔。

## 貢獻指南

1. Fork 本倉庫
2. 創建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打開 Pull Request

## 許可證

本項目採用 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情

## 聯繫方式

如有問題或建議，請通過以下方式聯繫：
- 創建 Issue
- 發送郵件

---

**最後更新**: 2025-01-27
