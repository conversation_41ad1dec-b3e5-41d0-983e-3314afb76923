'use client';

import React, { useState } from 'react';
import { EyeIcon, TruckIcon, ClockIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { useOrderStore } from '../../../store/orderStore';
import { OrderStatus } from '../../../types/order';
import ShippingModal from '../../../components/Order/ShippingModal';

export default function AdminOrdersPage() {
  const { orders, getOrdersByStatus, getOrdersReadyToShip } = useOrderStore();
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | 'ALL' | 'READY_TO_SHIP'>('ALL');
  const [shippingModal, setShippingModal] = useState<{
    isOpen: boolean;
    orderId?: number;
    orderNumber?: string;
  }>({ isOpen: false });

  // 獲取過濾後的訂單
  const getFilteredOrders = () => {
    switch (selectedStatus) {
      case 'ALL':
        return orders;
      case 'READY_TO_SHIP':
        return getOrdersReadyToShip();
      default:
        return getOrdersByStatus(selectedStatus);
    }
  };

  const filteredOrders = getFilteredOrders();

  // 狀態過濾選項
  const statusFilters = [
    { key: 'ALL', label: '全部訂單', count: orders.length },
    { key: 'READY_TO_SHIP', label: '待發貨', count: getOrdersReadyToShip().length },
    { key: OrderStatus.PENDING, label: '待付款', count: getOrdersByStatus(OrderStatus.PENDING).length },
    { key: OrderStatus.PAID, label: '已付款', count: getOrdersByStatus(OrderStatus.PAID).length },
    { key: OrderStatus.PROCESSING, label: '處理中', count: getOrdersByStatus(OrderStatus.PROCESSING).length },
    { key: OrderStatus.SHIPPED, label: '已發貨', count: getOrdersByStatus(OrderStatus.SHIPPED).length },
    { key: OrderStatus.DELIVERED, label: '已送達', count: getOrdersByStatus(OrderStatus.DELIVERED).length },
    { key: OrderStatus.CANCELLED, label: '已取消', count: getOrdersByStatus(OrderStatus.CANCELLED).length },
  ];

  // 獲取訂單狀態信息
  const getStatusInfo = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return { text: '待付款', color: 'text-orange-600', bgColor: 'bg-orange-100', icon: ClockIcon };
      case OrderStatus.PAID:
        return { text: '已付款', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: CheckCircleIcon };
      case OrderStatus.PROCESSING:
        return { text: '處理中', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: ClockIcon };
      case OrderStatus.SHIPPED:
        return { text: '已發貨', color: 'text-purple-600', bgColor: 'bg-purple-100', icon: TruckIcon };
      case OrderStatus.DELIVERED:
        return { text: '已送達', color: 'text-green-600', bgColor: 'bg-green-100', icon: CheckCircleIcon };
      case OrderStatus.CANCELLED:
        return { text: '已取消', color: 'text-red-600', bgColor: 'bg-red-100', icon: ClockIcon };
      default:
        return { text: '未知', color: 'text-gray-600', bgColor: 'bg-gray-100', icon: ClockIcon };
    }
  };

  // 檢查訂單是否可以發貨
  const canShipOrder = (status: OrderStatus) => {
    return status === OrderStatus.PAID || status === OrderStatus.PROCESSING;
  };

  // 打開發貨模態框
  const openShippingModal = (orderId: number, orderNumber: string) => {
    setShippingModal({
      isOpen: true,
      orderId,
      orderNumber
    });
  };

  // 關閉發貨模態框
  const closeShippingModal = () => {
    setShippingModal({ isOpen: false });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 頁面標題 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">訂單管理</h1>
        <p className="text-gray-600 mt-1">管理所有訂單和發貨操作</p>
      </div>

      {/* 狀態過濾 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div className="flex flex-wrap gap-2">
          {statusFilters.map((filter) => (
            <button
              key={filter.key}
              onClick={() => setSelectedStatus(filter.key as any)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === filter.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {filter.label}
              {filter.count > 0 && (
                <span className={`ml-1 ${
                  selectedStatus === filter.key ? 'text-blue-200' : 'text-gray-500'
                }`}>
                  ({filter.count})
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* 訂單列表 */}
      {filteredOrders.length === 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <div className="text-gray-400 mb-4">
            <ClockIcon className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暫無訂單</h3>
          <p className="text-gray-600">
            {selectedStatus === 'ALL' ? '暫無任何訂單' : `暫無${statusFilters.find(f => f.key === selectedStatus)?.label}訂單`}
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    訂單信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    商品數量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    訂單金額
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    狀態
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    下單時間
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredOrders.map((order) => {
                  const statusInfo = getStatusInfo(order.status);
                  const StatusIcon = statusInfo.icon;
                  
                  return (
                    <tr key={order.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {order.orderNumber}
                          </div>
                          <div className="text-sm text-gray-500">
                            用戶ID: {order.userId || 'N/A'}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {order.items.reduce((sum, item) => sum + item.quantity, 0)} 件
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ¥{order.totalAmount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color}`}>
                          <StatusIcon className="w-3 h-3" />
                          <span>{statusInfo.text}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(order.createdAt).toLocaleString('zh-CN')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                        <button className="text-blue-600 hover:text-blue-900 inline-flex items-center">
                          <EyeIcon className="w-4 h-4 mr-1" />
                          查看
                        </button>
                        {canShipOrder(order.status) && (
                          <button
                            onClick={() => openShippingModal(order.id!, order.orderNumber)}
                            className="text-green-600 hover:text-green-900 inline-flex items-center"
                          >
                            <TruckIcon className="w-4 h-4 mr-1" />
                            發貨
                          </button>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 發貨模態框 */}
      <ShippingModal
        isOpen={shippingModal.isOpen}
        onClose={closeShippingModal}
        orderId={shippingModal.orderId!}
        orderNumber={shippingModal.orderNumber!}
      />
    </div>
  );
}
