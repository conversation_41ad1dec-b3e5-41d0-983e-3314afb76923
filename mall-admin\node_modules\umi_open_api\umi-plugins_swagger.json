{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "http://localhost:8080", "description": "Generated server url"}], "paths": {"/api/products/{id}": {"get": {"tags": ["product-controller"], "operationId": "getProductById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Product"}}}}}}, "put": {"tags": ["product-controller"], "operationId": "updateProduct", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}, "delete": {"tags": ["product-controller"], "operationId": "deleteProduct", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/products": {"get": {"tags": ["product-controller"], "operationId": "getAllProducts", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}}}}, "post": {"tags": ["product-controller"], "operationId": "createProduct", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Product"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/api/orders": {"get": {"tags": ["order-controller"], "operationId": "getAllOrders", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}}}}}}, "post": {"tags": ["order-controller"], "operationId": "createOrder", "parameters": [{"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Order"}}}}}}}, "/api/auth/register": {"post": {"tags": ["auth-controller"], "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/auth/login": {"post": {"tags": ["auth-controller"], "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/orders/{id}/status": {"patch": {"tags": ["order-controller"], "operationId": "updateOrderStatus", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderStatusUpdateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK"}}}}, "/api/products/search": {"get": {"tags": ["product-controller"], "operationId": "searchProducts", "parameters": [{"name": "keyword", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}}}}}, "/api/orders/{id}": {"get": {"tags": ["order-controller"], "operationId": "getOrderById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/Order"}}}}}}, "delete": {"tags": ["order-controller"], "operationId": "cancelOrder", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/orders/user/{userId}": {"get": {"tags": ["order-controller"], "operationId": "getOrdersByUserId", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "Authorization", "in": "header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}}}}}}}, "/api/auth/user/{username}": {"get": {"tags": ["auth-controller"], "operationId": "getUserByUsername", "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}, "/api/auth/profile": {"get": {"tags": ["auth-controller"], "operationId": "getCurrentUserProfile", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}}}}}}, "components": {"schemas": {"Product": {"required": ["name", "price", "stock"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "description": {"type": "string"}, "price": {"type": "number"}, "stock": {"type": "integer", "format": "int32"}, "categoryId": {"type": "integer", "format": "int32"}, "imageUrl": {"type": "string"}, "tags": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "Order": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "userId": {"type": "integer", "format": "int64"}, "orderCode": {"type": "string"}, "totalAmount": {"type": "number"}, "shippingAddressLine1": {"type": "string"}, "shippingAddressLine2": {"type": "string"}, "shippingCity": {"type": "string"}, "shippingPostalCode": {"type": "string"}, "shippingCountry": {"type": "string"}, "paymentMethod": {"type": "string"}, "paymentStatus": {"type": "string"}, "notes": {"type": "string"}, "orderItems": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItem"}}, "status": {"$ref": "#/components/schemas/OrderStatus"}, "statusId": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "OrderItem": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "orderId": {"type": "integer", "format": "int64"}, "productId": {"type": "integer", "format": "int64"}, "quantity": {"type": "integer", "format": "int32"}, "price": {"type": "number"}, "priceAtPurchase": {"type": "number"}, "productNameAtPurchase": {"type": "string"}, "order": {"$ref": "#/components/schemas/Order"}}}, "OrderStatus": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "statusName": {"type": "string"}, "description": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}}, "RegisterRequest": {"required": ["confirmPassword", "email", "password", "username"], "type": "object", "properties": {"username": {"maxLength": 20, "minLength": 3, "type": "string"}, "password": {"maxLength": 100, "minLength": 6, "type": "string"}, "confirmPassword": {"type": "string"}, "email": {"type": "string"}, "passwordMatching": {"type": "boolean"}}}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}}, "OrderStatusUpdateRequest": {"type": "object", "properties": {"statusName": {"type": "string"}}}}}}