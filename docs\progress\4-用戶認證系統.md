# 4-用戶認證系統

**方案編號**: 4  
**開始日期**: 2025-01-27  
**預計完成**: 2025-01-28  
**狀態**: 🔄 進行中 (60%)  
**負責人**: AI Assistant  
**上級方案**: [0-總方案](./0-總方案.md)  
**前置依賴**: [3-訂單系統](./3-訂單系統.md)

## 方案概述

完善用戶認證系統，解決當前的數據庫結構問題，實現完整的用戶註冊、登錄、權限管理功能，為個性化功能和數據同步奠定基礎。

## 當前問題

### 已識別問題
- ❌ **數據庫結構問題**: users表缺少role字段，導致用戶註冊失敗
- ⚠️ **角色管理**: 需要實現更靈活的多角色管理系統
- ⚠️ **前端集成**: 用戶狀態管理和頁面集成待完善

### 解決方案
- 更新數據庫結構，採用roles表和user_roles關聯表
- 重構後端用戶管理代碼
- 開發前端用戶認證組件和頁面

## 技術方案

### 數據庫設計
- **roles表**: 角色定義（USER, ADMIN, SELLER, MODERATOR）
- **user_roles表**: 用戶角色關聯表（支持多角色）
- **users表**: 移除role字段，通過關聯表管理角色

### 後端架構
- **Spring Security**: 身份驗證和授權
- **JWT**: 無狀態token認證
- **MyBatis**: 數據庫操作
- **角色權限**: 基於角色的訪問控制

### 前端架構
- **用戶狀態管理**: Zustand + 持久化
- **路由保護**: Next.js中間件
- **表單驗證**: 客戶端和服務端雙重驗證

## 實施計劃

### 階段1：數據庫結構修復 ✅
- [x] 創建數據庫檢查腳本 (check_database_structure.sql)
- [x] 創建數據初始化腳本 (initialize_basic_data.sql)
- [x] 設計新的角色管理表結構

### 階段2：後端代碼重構 ✅
- [x] 創建Role實體類和RoleMapper
- [x] 更新User類支持多角色
- [x] 修改UserMapper移除role字段依賴
- [x] 更新UserServiceImpl處理角色分配
- [x] 更新AuthController返回角色信息

### 階段3：前端用戶系統開發 📋
- [ ] 創建用戶狀態管理 (userStore.ts)
- [ ] 開發登錄頁面 (/login)
- [ ] 開發註冊頁面 (/register)
- [ ] 開發用戶個人中心 (/profile)
- [ ] 實現JWT token管理

### 階段4：系統集成和測試 📋
- [ ] 路由保護中間件
- [ ] Header用戶狀態顯示
- [ ] 登錄狀態持久化
- [ ] 權限控制集成
- [ ] 完整功能測試

## 已完成工作

### 1. 數據庫結構設計 ✅

#### 新增表結構
```sql
-- 角色表
CREATE TABLE `roles` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(50) NOT NULL UNIQUE,
  `description` TEXT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用戶角色關聯表
CREATE TABLE `user_roles` (
  `user_id` BIGINT NOT NULL,
  `role_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `role_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
  FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`)
);
```

#### 基礎數據
- USER, ADMIN, SELLER, MODERATOR 角色
- 為現有用戶分配默認USER角色

### 2. 後端代碼重構 ✅

#### Role實體類和Mapper
```java
public class Role {
    private Integer id;
    private String name;
    private String description;
    // getters, setters, 構造函數
}

@Mapper
public interface RoleMapper {
    List<Role> findRolesByUserId(Long userId);
    int assignRoleToUser(Long userId, Integer roleId);
    // 其他角色管理方法
}
```

#### User類更新
```java
public class User implements UserDetails {
    private List<Role> roles = new ArrayList<>();
    
    public boolean hasRole(String roleName) {
        return roles.stream().anyMatch(role -> role.getName().equals(roleName));
    }
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 基於角色列表生成權限
    }
}
```

### 3. 創建的腳本文件 ✅
- **check_database_structure.sql**: 檢查數據庫結構
- **initialize_basic_data.sql**: 初始化角色和基礎數據
- **fix_users_table.sql**: 修復用戶表結構（備用）

## 待完成工作

### 1. 前端用戶狀態管理
```typescript
interface UserState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials) => Promise<void>;
  logout: () => void;
  register: (userData) => Promise<void>;
  updateProfile: (data) => Promise<void>;
}
```

### 2. 用戶認證頁面
- **登錄頁面**: 用戶名/密碼登錄，記住我功能
- **註冊頁面**: 用戶註冊表單，驗證邏輯
- **個人中心**: 用戶信息展示和編輯

### 3. 權限控制
- **路由保護**: 保護需要登錄的頁面
- **組件權限**: 基於角色顯示/隱藏功能
- **API權限**: 請求攔截和權限驗證

### 4. 用戶體驗
- **登錄狀態持久化**: 刷新頁面保持登錄
- **自動登錄**: 記住我功能
- **登出處理**: 清理用戶數據

## 技術實現細節

### JWT Token管理
```typescript
// Token存儲和管理
const tokenManager = {
  setToken: (token: string) => localStorage.setItem('token', token),
  getToken: () => localStorage.getItem('token'),
  removeToken: () => localStorage.removeItem('token'),
  isTokenValid: (token: string) => {
    // JWT token驗證邏輯
  }
};
```

### 路由保護中間件
```typescript
// Next.js中間件保護路由
export function middleware(request: NextRequest) {
  const token = request.cookies.get('token');
  const isAuthPage = request.nextUrl.pathname.startsWith('/auth');
  const isProtectedPage = protectedRoutes.includes(request.nextUrl.pathname);
  
  // 路由保護邏輯
}
```

## 測試計劃

### 數據庫測試
- [ ] 執行數據庫結構檢查
- [ ] 驗證角色數據初始化
- [ ] 測試用戶註冊功能

### 後端API測試
- [ ] 用戶註冊API測試
- [ ] 用戶登錄API測試
- [ ] 角色權限驗證測試
- [ ] JWT token驗證測試

### 前端功能測試
- [ ] 登錄頁面功能測試
- [ ] 註冊頁面功能測試
- [ ] 用戶狀態管理測試
- [ ] 路由保護測試

### 集成測試
- [ ] 前後端登錄流程測試
- [ ] 權限控制集成測試
- [ ] 用戶體驗完整性測試

## 風險評估

### 技術風險
- **中等**: 數據庫結構變更可能影響現有數據
- **低**: JWT實現相對標準
- **中等**: 多角色權限控制複雜度

### 業務風險
- **低**: 用戶認證是標準功能
- **中等**: 用戶體驗需要細致打磨

## 成功標準

### 功能標準
- [x] 數據庫結構更新成功
- [x] 後端代碼重構完成
- [ ] 用戶註冊功能正常
- [ ] 用戶登錄功能正常
- [ ] 權限控制有效
- [ ] 用戶體驗流暢

### 技術標準
- [ ] API響應時間 < 500ms
- [ ] 前端頁面加載 < 2秒
- [ ] 錯誤處理完善
- [ ] 安全性驗證通過

## 下一步行動

### 立即執行
1. **數據庫更新**: 執行SQL腳本修復數據庫結構
2. **後端測試**: 重啟服務並測試用戶註冊
3. **問題修復**: 根據測試結果調整代碼

### 後續開發
1. **前端開發**: 創建用戶認證相關頁面和組件
2. **系統集成**: 實現完整的認證流程
3. **測試驗證**: 全面測試用戶認證功能

---

**當前進度**: 60% (數據庫和後端重構完成)  
**下次更新**: 2025-01-28  
**下一步**: 執行數據庫腳本並測試後端功能
