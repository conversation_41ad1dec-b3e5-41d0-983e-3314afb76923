package io.github.roshad.ecommerce.shipping;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingInfo {
    private Long id;
    private Long orderId;
    private String trackingNumber;
    private String carrier;
    private String shippingMethod;
    private LocalDateTime shippedAt;
    private LocalDateTime estimatedDelivery;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 常用快遞公司常量
    public static final String CARRIER_SF = "順豐速運";
    public static final String CARRIER_YTO = "圓通速遞";
    public static final String CARRIER_ZTO = "中通快遞";
    public static final String CARRIER_STO = "申通快遞";
    public static final String CARRIER_EMS = "中國郵政EMS";
    public static final String CARRIER_JD = "京東物流";
    
    // 配送方式常量
    public static final String METHOD_STANDARD = "標準配送";
    public static final String METHOD_EXPRESS = "加急配送";
    public static final String METHOD_SAME_DAY = "當日達";
    public static final String METHOD_NEXT_DAY = "次日達";
}
