'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { EyeIcon, ClockIcon, CheckCircleIcon, TruckIcon } from '@heroicons/react/24/outline';
import { useOrderStore } from '../../store/orderStore';
import { OrderStatus } from '../../types/order';
import ShippingModal from '../../components/Order/ShippingModal';

export default function OrdersPage() {
  const { orders, getUserOrders, getOrdersByStatus } = useOrderStore();
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus | 'ALL'>('ALL');
  const [shippingModal, setShippingModal] = useState<{
    isOpen: boolean;
    orderId?: number;
    orderNumber?: string;
  }>({ isOpen: false });

  // 模拟管理员权限检查（实际应该从认证状态获取）
  const isAdmin = true; // 这里应该从用户认证状态获取

  // 检查订单是否可以发货
  const canShipOrder = (status: OrderStatus) => {
    return status === OrderStatus.PAID || status === OrderStatus.PROCESSING;
  };

  // 打开发货模态框
  const openShippingModal = (orderId: number, orderNumber: string) => {
    setShippingModal({
      isOpen: true,
      orderId,
      orderNumber
    });
  };

  // 关闭发货模态框
  const closeShippingModal = () => {
    setShippingModal({ isOpen: false });
  };

  // 獲取過濾後的訂單
  const filteredOrders = selectedStatus === 'ALL' 
    ? getUserOrders() 
    : getOrdersByStatus(selectedStatus);

  // 狀態過濾選項
  const statusFilters = [
    { key: 'ALL', label: '全部', count: orders.length },
    { key: OrderStatus.PENDING, label: '待付款', count: getOrdersByStatus(OrderStatus.PENDING).length },
    { key: OrderStatus.PAID, label: '已付款', count: getOrdersByStatus(OrderStatus.PAID).length },
    { key: OrderStatus.SHIPPED, label: '已發貨', count: getOrdersByStatus(OrderStatus.SHIPPED).length },
    { key: OrderStatus.DELIVERED, label: '已送達', count: getOrdersByStatus(OrderStatus.DELIVERED).length },
    { key: OrderStatus.CANCELLED, label: '已取消', count: getOrdersByStatus(OrderStatus.CANCELLED).length },
  ];

  // 獲取訂單狀態信息
  const getStatusInfo = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return { text: '待付款', color: 'text-orange-600', bgColor: 'bg-orange-100', icon: ClockIcon };
      case OrderStatus.PAID:
        return { text: '已付款', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: CheckCircleIcon };
      case OrderStatus.PROCESSING:
        return { text: '處理中', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: ClockIcon };
      case OrderStatus.SHIPPED:
        return { text: '已發貨', color: 'text-purple-600', bgColor: 'bg-purple-100', icon: TruckIcon };
      case OrderStatus.DELIVERED:
        return { text: '已送達', color: 'text-green-600', bgColor: 'bg-green-100', icon: CheckCircleIcon };
      case OrderStatus.CANCELLED:
        return { text: '已取消', color: 'text-red-600', bgColor: 'bg-red-100', icon: ClockIcon };
      default:
        return { text: '未知', color: 'text-gray-600', bgColor: 'bg-gray-100', icon: ClockIcon };
    }
  };

  // 處理圖片URL
  const getImageUrl = (url: string, productName: string) => {
    if (!url) return '/placeholder.jpg';
    
    if (url.startsWith('/images/')) {
      return `https://via.placeholder.com/60x60/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
    }
    
    try {
      new URL(url);
      return url;
    } catch {
      return `https://via.placeholder.com/60x60/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 頁面標題 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">我的訂單</h1>
        <p className="text-gray-600 mt-1">查看和管理您的所有訂單</p>
      </div>

      {/* 狀態過濾 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
        <div className="flex flex-wrap gap-2">
          {statusFilters.map((filter) => (
            <button
              key={filter.key}
              onClick={() => setSelectedStatus(filter.key as OrderStatus | 'ALL')}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedStatus === filter.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {filter.label}
              {filter.count > 0 && (
                <span className={`ml-1 ${
                  selectedStatus === filter.key ? 'text-blue-200' : 'text-gray-500'
                }`}>
                  ({filter.count})
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* 訂單列表 */}
      {filteredOrders.length === 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <div className="text-gray-400 mb-4">
            <ClockIcon className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暫無訂單</h3>
          <p className="text-gray-600 mb-6">
            {selectedStatus === 'ALL' ? '您還沒有任何訂單' : `暫無${statusFilters.find(f => f.key === selectedStatus)?.label}訂單`}
          </p>
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            去購物
          </Link>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredOrders.map((order) => {
            const statusInfo = getStatusInfo(order.status);
            const StatusIcon = statusInfo.icon;
            
            return (
              <div key={order.id} className="bg-white rounded-lg border border-gray-200 p-6">
                {/* 訂單頭部 */}
                <div className="flex items-center justify-between mb-4 pb-4 border-b border-gray-100">
                  <div className="flex items-center space-x-4">
                    <div>
                      <p className="text-sm text-gray-600">訂單號</p>
                      <p className="font-medium text-gray-900">{order.orderNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">下單時間</p>
                      <p className="font-medium text-gray-900">
                        {new Date(order.createdAt).toLocaleDateString('zh-CN')}
                      </p>
                    </div>
                  </div>
                  
                  <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${statusInfo.bgColor}`}>
                    <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
                    <span className={`text-sm font-medium ${statusInfo.color}`}>
                      {statusInfo.text}
                    </span>
                  </div>
                </div>

                {/* 商品列表 */}
                <div className="space-y-3 mb-4">
                  {order.items.slice(0, 3).map((item) => (
                    <div key={item.id} className="flex items-center space-x-3">
                      <div className="relative w-12 h-12 flex-shrink-0">
                        <Image
                          src={getImageUrl(item.productImage, item.productName)}
                          alt={item.productName}
                          fill
                          style={{ objectFit: "cover" }}
                          className="rounded-md"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 line-clamp-1">
                          {item.productName}
                        </p>
                        <p className="text-xs text-gray-500">
                          ¥{item.price.toFixed(2)} × {item.quantity}
                        </p>
                      </div>
                      <div className="text-sm font-medium text-gray-900">
                        ¥{item.subtotal.toFixed(2)}
                      </div>
                    </div>
                  ))}
                  
                  {order.items.length > 3 && (
                    <p className="text-sm text-gray-500 text-center">
                      還有 {order.items.length - 3} 件商品...
                    </p>
                  )}
                </div>

                {/* 訂單底部 */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="text-sm text-gray-600">
                    共 {order.items.reduce((sum, item) => sum + item.quantity, 0)} 件商品
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-sm text-gray-600">訂單總額</p>
                      <p className="text-lg font-semibold text-red-600">
                        ¥{order.totalAmount.toFixed(2)}
                      </p>
                    </div>

                    <div className="flex space-x-2">
                      {/* 管理员发货按钮 */}
                      {isAdmin && canShipOrder(order.status) && (
                        <button
                          onClick={() => openShippingModal(order.id!, order.orderNumber)}
                          className="flex items-center space-x-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                        >
                          <TruckIcon className="w-4 h-4" />
                          <span>發貨</span>
                        </button>
                      )}

                      <Link
                        href={`/orders/${order.id}`}
                        className="flex items-center space-x-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <EyeIcon className="w-4 h-4" />
                        <span>查看詳情</span>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* 返回購物按鈕 */}
      {filteredOrders.length > 0 && (
        <div className="mt-8 text-center">
          <Link
            href="/"
            className="inline-flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            繼續購物
          </Link>
        </div>
      )}

      {/* 發貨模態框 */}
      <ShippingModal
        isOpen={shippingModal.isOpen}
        onClose={closeShippingModal}
        orderId={shippingModal.orderId!}
        orderNumber={shippingModal.orderNumber!}
      />
    </div>
  );
}
