package io.github.roshad.ecommerce.shipping;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingRequest {
    
    @NotBlank(message = "運單號不能為空")
    private String trackingNumber;
    
    @NotBlank(message = "快遞公司不能為空")
    private String carrier;
    
    @NotBlank(message = "配送方式不能為空")
    private String shippingMethod;
    
    private String notes;
    
    // 預計送達天數（用於計算預計送達時間）
    private Integer estimatedDays;
}
