'use client';

import React, { useState } from 'react';
import { XMarkIcon, TruckIcon } from '@heroicons/react/24/outline';
import { useOrderStore } from '../../store/orderStore';
import { ShippingRequest, CARRIERS, SHIPPING_METHODS } from '../../types/order';
import toast from 'react-hot-toast';

interface ShippingModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderId: number;
  orderNumber: string;
}

export default function ShippingModal({ isOpen, onClose, orderId, orderNumber }: ShippingModalProps) {
  const { shipOrder, isLoading } = useOrderStore();
  const [formData, setFormData] = useState<ShippingRequest>({
    trackingNumber: '',
    carrier: '',
    shippingMethod: '',
    notes: '',
    estimatedDays: 3
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.trackingNumber.trim()) {
      toast.error('請輸入運單號');
      return;
    }
    
    if (!formData.carrier) {
      toast.error('請選擇快遞公司');
      return;
    }
    
    if (!formData.shippingMethod) {
      toast.error('請選擇配送方式');
      return;
    }

    try {
      await shipOrder(orderId, formData);
      toast.success('發貨成功！');
      onClose();
      // 重置表單
      setFormData({
        trackingNumber: '',
        carrier: '',
        shippingMethod: '',
        notes: '',
        estimatedDays: 3
      });
    } catch (error) {
      toast.error('發貨失敗，請重試');
    }
  };

  const handleInputChange = (field: keyof ShippingRequest, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* 背景遮罩 */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* 模態框 */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          {/* 標題 */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <TruckIcon className="w-6 h-6 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">訂單發貨</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          {/* 訂單信息 */}
          <div className="mb-6 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">訂單號</p>
            <p className="font-medium text-gray-900">{orderNumber}</p>
          </div>

          {/* 發貨表單 */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 運單號 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                運單號 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.trackingNumber}
                onChange={(e) => handleInputChange('trackingNumber', e.target.value)}
                placeholder="請輸入運單號"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* 快遞公司 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                快遞公司 <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.carrier}
                onChange={(e) => handleInputChange('carrier', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">請選擇快遞公司</option>
                {Object.entries(CARRIERS).map(([key, value]) => (
                  <option key={key} value={value}>{value}</option>
                ))}
              </select>
            </div>

            {/* 配送方式 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                配送方式 <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.shippingMethod}
                onChange={(e) => handleInputChange('shippingMethod', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">請選擇配送方式</option>
                {Object.entries(SHIPPING_METHODS).map(([key, value]) => (
                  <option key={key} value={value}>{value}</option>
                ))}
              </select>
            </div>

            {/* 預計送達天數 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                預計送達天數
              </label>
              <input
                type="number"
                min="1"
                max="30"
                value={formData.estimatedDays || ''}
                onChange={(e) => handleInputChange('estimatedDays', parseInt(e.target.value) || 0)}
                placeholder="預計送達天數"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* 備註 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                備註
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="發貨備註（可選）"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* 操作按鈕 */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
                disabled={isLoading}
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? '發貨中...' : '確認發貨'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
