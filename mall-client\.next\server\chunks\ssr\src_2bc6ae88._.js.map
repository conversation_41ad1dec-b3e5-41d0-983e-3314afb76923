{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/store/addressStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { Address } from '../types/order';\n\n// 地址管理狀態接口\ninterface AddressState {\n  addresses: Address[];\n  selectedAddressId: number | null;\n  \n  // 操作方法\n  addAddress: (address: Omit<Address, 'id'>) => void;\n  updateAddress: (id: number, address: Partial<Address>) => void;\n  removeAddress: (id: number) => void;\n  setDefaultAddress: (id: number) => void;\n  selectAddress: (id: number) => void;\n  getDefaultAddress: () => Address | null;\n  getSelectedAddress: () => Address | null;\n  clearAddresses: () => void;\n}\n\n// 生成臨時ID（實際項目中應該由後端生成）\nlet tempId = 1;\n\n// 創建地址管理 store\nexport const useAddressStore = create<AddressState>()(\n  persist(\n    (set, get) => ({\n      addresses: [],\n      selectedAddressId: null,\n\n      // 添加地址\n      addAddress: (address) => {\n        const { addresses } = get();\n        const newAddress: Address = {\n          ...address,\n          id: tempId++,\n          // 如果是第一個地址，自動設為默認\n          isDefault: addresses.length === 0 ? true : address.isDefault\n        };\n\n        // 如果新地址設為默認，取消其他地址的默認狀態\n        const updatedAddresses = address.isDefault\n          ? addresses.map(addr => ({ ...addr, isDefault: false }))\n          : addresses;\n\n        set({\n          addresses: [...updatedAddresses, newAddress],\n          // 如果沒有選中的地址，自動選中新添加的地址\n          selectedAddressId: get().selectedAddressId || newAddress.id!\n        });\n      },\n\n      // 更新地址\n      updateAddress: (id, updatedAddress) => {\n        const { addresses } = get();\n        \n        const newAddresses = addresses.map(address => {\n          if (address.id === id) {\n            const updated = { ...address, ...updatedAddress };\n            \n            // 如果設為默認地址，取消其他地址的默認狀態\n            if (updatedAddress.isDefault) {\n              return updated;\n            }\n            return updated;\n          }\n          \n          // 如果當前更新的地址設為默認，其他地址取消默認\n          if (updatedAddress.isDefault) {\n            return { ...address, isDefault: false };\n          }\n          \n          return address;\n        });\n\n        set({ addresses: newAddresses });\n      },\n\n      // 刪除地址\n      removeAddress: (id) => {\n        const { addresses, selectedAddressId } = get();\n        const addressToRemove = addresses.find(addr => addr.id === id);\n        const newAddresses = addresses.filter(address => address.id !== id);\n        \n        // 如果刪除的是默認地址，設置第一個地址為默認\n        if (addressToRemove?.isDefault && newAddresses.length > 0) {\n          newAddresses[0].isDefault = true;\n        }\n\n        set({\n          addresses: newAddresses,\n          // 如果刪除的是當前選中的地址，重置選中狀態\n          selectedAddressId: selectedAddressId === id \n            ? (newAddresses.length > 0 ? newAddresses[0].id! : null)\n            : selectedAddressId\n        });\n      },\n\n      // 設置默認地址\n      setDefaultAddress: (id) => {\n        const { addresses } = get();\n        const newAddresses = addresses.map(address => ({\n          ...address,\n          isDefault: address.id === id\n        }));\n\n        set({ addresses: newAddresses });\n      },\n\n      // 選擇地址（用於結算）\n      selectAddress: (id) => {\n        set({ selectedAddressId: id });\n      },\n\n      // 獲取默認地址\n      getDefaultAddress: () => {\n        const { addresses } = get();\n        return addresses.find(address => address.isDefault) || null;\n      },\n\n      // 獲取當前選中的地址\n      getSelectedAddress: () => {\n        const { addresses, selectedAddressId } = get();\n        if (!selectedAddressId) return null;\n        return addresses.find(address => address.id === selectedAddressId) || null;\n      },\n\n      // 清空所有地址\n      clearAddresses: () => {\n        set({\n          addresses: [],\n          selectedAddressId: null\n        });\n      }\n    }),\n    {\n      name: 'address-storage',\n      version: 1,\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAmBA,uBAAuB;AACvB,IAAI,SAAS;AAGN,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAClC,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,WAAW,EAAE;QACb,mBAAmB;QAEnB,OAAO;QACP,YAAY,CAAC;YACX,MAAM,EAAE,SAAS,EAAE,GAAG;YACtB,MAAM,aAAsB;gBAC1B,GAAG,OAAO;gBACV,IAAI;gBACJ,kBAAkB;gBAClB,WAAW,UAAU,MAAM,KAAK,IAAI,OAAO,QAAQ,SAAS;YAC9D;YAEA,wBAAwB;YACxB,MAAM,mBAAmB,QAAQ,SAAS,GACtC,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC,KACpD;YAEJ,IAAI;gBACF,WAAW;uBAAI;oBAAkB;iBAAW;gBAC5C,uBAAuB;gBACvB,mBAAmB,MAAM,iBAAiB,IAAI,WAAW,EAAE;YAC7D;QACF;QAEA,OAAO;QACP,eAAe,CAAC,IAAI;YAClB,MAAM,EAAE,SAAS,EAAE,GAAG;YAEtB,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA;gBACjC,IAAI,QAAQ,EAAE,KAAK,IAAI;oBACrB,MAAM,UAAU;wBAAE,GAAG,OAAO;wBAAE,GAAG,cAAc;oBAAC;oBAEhD,uBAAuB;oBACvB,IAAI,eAAe,SAAS,EAAE;wBAC5B,OAAO;oBACT;oBACA,OAAO;gBACT;gBAEA,yBAAyB;gBACzB,IAAI,eAAe,SAAS,EAAE;oBAC5B,OAAO;wBAAE,GAAG,OAAO;wBAAE,WAAW;oBAAM;gBACxC;gBAEA,OAAO;YACT;YAEA,IAAI;gBAAE,WAAW;YAAa;QAChC;QAEA,OAAO;QACP,eAAe,CAAC;YACd,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG;YACzC,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC3D,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;YAEhE,wBAAwB;YACxB,IAAI,iBAAiB,aAAa,aAAa,MAAM,GAAG,GAAG;gBACzD,YAAY,CAAC,EAAE,CAAC,SAAS,GAAG;YAC9B;YAEA,IAAI;gBACF,WAAW;gBACX,uBAAuB;gBACvB,mBAAmB,sBAAsB,KACpC,aAAa,MAAM,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,EAAE,GAAI,OACjD;YACN;QACF;QAEA,SAAS;QACT,mBAAmB,CAAC;YAClB,MAAM,EAAE,SAAS,EAAE,GAAG;YACtB,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC7C,GAAG,OAAO;oBACV,WAAW,QAAQ,EAAE,KAAK;gBAC5B,CAAC;YAED,IAAI;gBAAE,WAAW;YAAa;QAChC;QAEA,aAAa;QACb,eAAe,CAAC;YACd,IAAI;gBAAE,mBAAmB;YAAG;QAC9B;QAEA,SAAS;QACT,mBAAmB;YACjB,MAAM,EAAE,SAAS,EAAE,GAAG;YACtB,OAAO,UAAU,IAAI,CAAC,CAAA,UAAW,QAAQ,SAAS,KAAK;QACzD;QAEA,YAAY;QACZ,oBAAoB;YAClB,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,GAAG;YACzC,IAAI,CAAC,mBAAmB,OAAO;YAC/B,OAAO,UAAU,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK,sBAAsB;QACxE;QAEA,SAAS;QACT,gBAAgB;YACd,IAAI;gBACF,WAAW,EAAE;gBACb,mBAAmB;YACrB;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS;AACX", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/types/order.ts"], "sourcesContent": ["// 訂單相關類型定義\n\n// 訂單狀態枚舉\nexport enum OrderStatus {\n  PENDING = 'PENDING',           // 待付款\n  PAID = 'PAID',                 // 已付款\n  PROCESSING = 'PROCESSING',     // 處理中\n  SHIPPED = 'SHIPPED',           // 已發貨\n  DELIVERED = 'DELIVERED',       // 已送達\n  CANCELLED = 'CANCELLED',       // 已取消\n  REFUNDED = 'REFUNDED'          // 已退款\n}\n\n// 支付方式枚舉\nexport enum PaymentMethod {\n  CREDIT_CARD = 'CREDIT_CARD',   // 信用卡\n  DEBIT_CARD = 'DEBIT_CARD',     // 借記卡\n  PAYPAL = 'PAYPAL',             // PayPal\n  ALIPAY = 'ALIPAY',             // 支付寶\n  WECHAT_PAY = 'WECHAT_PAY',     // 微信支付\n  CASH_ON_DELIVERY = 'CASH_ON_DELIVERY' // 貨到付款\n}\n\n// 收貨地址接口\nexport interface Address {\n  id?: number;\n  userId?: number;\n  recipientName: string;         // 收件人姓名\n  phone: string;                 // 聯繫電話\n  province: string;              // 省份\n  city: string;                  // 城市\n  district: string;              // 區/縣\n  detailAddress: string;         // 詳細地址\n  postalCode?: string;           // 郵政編碼\n  isDefault: boolean;            // 是否為默認地址\n  createdAt?: string;\n  updatedAt?: string;\n}\n\n// 訂單商品項接口\nexport interface OrderItem {\n  id?: number;\n  orderId?: number;\n  productId: number;\n  productName: string;\n  productImage: string;\n  price: number;                 // 下單時的價格\n  quantity: number;\n  subtotal: number;              // 小計 (price * quantity)\n}\n\n// 訂單接口\nexport interface Order {\n  id?: number;\n  userId?: number;\n  orderNumber: string;           // 訂單號\n  status: OrderStatus;\n  items: OrderItem[];\n  \n  // 價格信息\n  subtotal: number;              // 商品小計\n  shippingFee: number;           // 運費\n  discount: number;              // 折扣金額\n  totalAmount: number;           // 總金額\n  \n  // 收貨信息\n  shippingAddress: Address;\n  \n  // 支付信息\n  paymentMethod: PaymentMethod;\n  paymentStatus: 'PENDING' | 'COMPLETED' | 'FAILED';\n  paidAt?: string;\n  \n  // 時間戳\n  createdAt: string;\n  updatedAt: string;\n  \n  // 備註\n  notes?: string;\n}\n\n// 創建訂單請求接口\nexport interface CreateOrderRequest {\n  items: {\n    productId: number;\n    quantity: number;\n  }[];\n  shippingAddressId: number;\n  paymentMethod: PaymentMethod;\n  notes?: string;\n}\n\n// 發貨信息接口\nexport interface ShippingInfo {\n  id?: number;\n  orderId: number;\n  trackingNumber: string;\n  carrier: string;\n  shippingMethod: string;\n  shippedAt: string;\n  estimatedDelivery?: string;\n  notes?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 發貨請求接口\nexport interface ShippingRequest {\n  trackingNumber: string;\n  carrier: string;\n  shippingMethod: string;\n  notes?: string;\n  estimatedDays?: number;\n}\n\n// 常用快遞公司\nexport const CARRIERS = {\n  SF: '順豐速運',\n  YTO: '圓通速遞',\n  ZTO: '中通快遞',\n  STO: '申通快遞',\n  EMS: '中國郵政EMS',\n  JD: '京東物流'\n} as const;\n\n// 配送方式\nexport const SHIPPING_METHODS = {\n  STANDARD: '標準配送',\n  EXPRESS: '加急配送',\n  SAME_DAY: '當日達',\n  NEXT_DAY: '次日達'\n} as const;\n\n// 訂單統計接口\nexport interface OrderSummary {\n  totalOrders: number;\n  pendingOrders: number;\n  completedOrders: number;\n  totalSpent: number;\n}\n"], "names": [], "mappings": "AAAA,WAAW;AAEX,SAAS;;;;;;;AACF,IAAA,AAAK,qCAAA;;;;;;;0CAOqB,MAAM;WAP3B;;AAWL,IAAA,AAAK,uCAAA;;;;;;4DAM4B,OAAO;WANnC;;AAsGL,MAAM,WAAW;IACtB,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;AACN;AAGO,MAAM,mBAAmB;IAC9B,UAAU;IACV,SAAS;IACT,UAAU;IACV,UAAU;AACZ", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/store/orderStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { Order, OrderStatus, CreateOrderRequest, OrderItem, ShippingInfo, ShippingRequest } from '../types/order';\n\n// 訂單管理狀態接口\ninterface OrderState {\n  orders: Order[];\n  currentOrder: Order | null;\n  isLoading: boolean;\n  error: string | null;\n\n  // 發貨信息緩存\n  shippingInfos: Map<number, ShippingInfo>; // orderId -> ShippingInfo\n\n  // 操作方法\n  createOrder: (orderData: CreateOrderRequest) => Promise<Order>;\n  getOrderById: (id: number) => Order | null;\n  getUserOrders: (userId?: number) => Order[];\n  updateOrderStatus: (orderId: number, status: OrderStatus) => void;\n  cancelOrder: (orderId: number) => void;\n  setCurrentOrder: (order: Order | null) => void;\n  clearOrders: () => void;\n\n  // 發貨相關方法\n  shipOrder: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;\n  getOrderShipping: (orderId: number) => Promise<ShippingInfo | null>;\n  updateOrderShipping: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;\n  getOrdersReadyToShip: () => Order[];\n\n  // 統計方法\n  getOrdersByStatus: (status: OrderStatus) => Order[];\n  getTotalSpent: () => number;\n}\n\n// 生成訂單號\nconst generateOrderNumber = (): string => {\n  const timestamp = Date.now();\n  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n  return `ORD${timestamp}${random}`;\n};\n\n// 生成臨時ID（實際項目中應該由後端生成）\nlet tempOrderId = 1;\n\n// 創建訂單管理 store\nexport const useOrderStore = create<OrderState>()(\n  persist(\n    (set, get) => ({\n      orders: [],\n      currentOrder: null,\n      isLoading: false,\n      error: null,\n      shippingInfos: new Map(),\n\n      // 創建訂單\n      createOrder: async (orderData: CreateOrderRequest) => {\n        set({ isLoading: true, error: null });\n        \n        try {\n          // 模擬API調用延遲\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          \n          // 這裡應該調用實際的API\n          // const response = await fetch('/api/orders', { method: 'POST', body: JSON.stringify(orderData) });\n          // const order = await response.json();\n          \n          // 模擬創建訂單\n          const now = new Date().toISOString();\n          const orderNumber = generateOrderNumber();\n          \n          // 計算訂單項（這裡需要從購物車或傳入的數據獲取商品信息）\n          const orderItems: OrderItem[] = orderData.items.map(item => ({\n            productId: item.productId,\n            productName: `商品 ${item.productId}`, // 實際應該從商品數據獲取\n            productImage: '/placeholder.jpg',\n            price: 99.99, // 實際應該從商品數據獲取\n            quantity: item.quantity,\n            subtotal: 99.99 * item.quantity\n          }));\n          \n          const subtotal = orderItems.reduce((sum, item) => sum + item.subtotal, 0);\n          const shippingFee = subtotal >= 99 ? 0 : 10;\n          const discount = 0;\n          const totalAmount = subtotal + shippingFee - discount;\n          \n          const newOrder: Order = {\n            id: tempOrderId++,\n            orderNumber,\n            status: OrderStatus.PENDING,\n            items: orderItems,\n            subtotal,\n            shippingFee,\n            discount,\n            totalAmount,\n            shippingAddress: {\n              id: orderData.shippingAddressId,\n              recipientName: '收件人',\n              phone: '13800138000',\n              province: '廣東省',\n              city: '深圳市',\n              district: '南山區',\n              detailAddress: '科技園南區',\n              isDefault: false\n            }, // 實際應該從地址數據獲取\n            paymentMethod: orderData.paymentMethod,\n            paymentStatus: 'PENDING',\n            createdAt: now,\n            updatedAt: now,\n            notes: orderData.notes\n          };\n          \n          const { orders } = get();\n          set({ \n            orders: [newOrder, ...orders],\n            currentOrder: newOrder,\n            isLoading: false \n          });\n          \n          return newOrder;\n        } catch (error) {\n          set({ \n            error: error instanceof Error ? error.message : '創建訂單失敗',\n            isLoading: false \n          });\n          throw error;\n        }\n      },\n\n      // 根據ID獲取訂單\n      getOrderById: (id: number) => {\n        const { orders } = get();\n        return orders.find(order => order.id === id) || null;\n      },\n\n      // 獲取用戶訂單\n      getUserOrders: (userId?: number) => {\n        const { orders } = get();\n        // 如果沒有傳入userId，返回所有訂單（模擬當前用戶的訂單）\n        return orders;\n      },\n\n      // 更新訂單狀態\n      updateOrderStatus: (orderId: number, status: OrderStatus) => {\n        const { orders } = get();\n        const updatedOrders = orders.map(order => {\n          if (order.id === orderId) {\n            const updatedOrder = {\n              ...order,\n              status,\n              updatedAt: new Date().toISOString()\n            };\n            \n            // 如果狀態變為已付款，更新支付狀態和時間\n            if (status === OrderStatus.PAID) {\n              updatedOrder.paymentStatus = 'COMPLETED';\n              updatedOrder.paidAt = new Date().toISOString();\n            }\n            \n            return updatedOrder;\n          }\n          return order;\n        });\n\n        set({ orders: updatedOrders });\n      },\n\n      // 取消訂單\n      cancelOrder: (orderId: number) => {\n        get().updateOrderStatus(orderId, OrderStatus.CANCELLED);\n      },\n\n      // 設置當前訂單\n      setCurrentOrder: (order: Order | null) => {\n        set({ currentOrder: order });\n      },\n\n      // 清空訂單\n      clearOrders: () => {\n        set({\n          orders: [],\n          currentOrder: null,\n          error: null\n        });\n      },\n\n      // 根據狀態獲取訂單\n      getOrdersByStatus: (status: OrderStatus) => {\n        const { orders } = get();\n        return orders.filter(order => order.status === status);\n      },\n\n      // 計算總消費\n      getTotalSpent: () => {\n        const { orders } = get();\n        return orders\n          .filter(order => order.status !== OrderStatus.CANCELLED)\n          .reduce((total, order) => total + order.totalAmount, 0);\n      },\n\n      // 發貨相關方法\n      shipOrder: async (orderId: number, shippingData: ShippingRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          // 模擬API調用\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // 實際應該調用後端API\n          // const response = await fetch(`/api/orders/${orderId}/ship`, {\n          //   method: 'POST',\n          //   headers: { 'Content-Type': 'application/json' },\n          //   body: JSON.stringify(shippingData)\n          // });\n          // const shippingInfo = await response.json();\n\n          // 模擬創建發貨信息\n          const now = new Date().toISOString();\n          const shippingInfo: ShippingInfo = {\n            id: Date.now(),\n            orderId,\n            trackingNumber: shippingData.trackingNumber,\n            carrier: shippingData.carrier,\n            shippingMethod: shippingData.shippingMethod,\n            shippedAt: now,\n            estimatedDelivery: shippingData.estimatedDays\n              ? new Date(Date.now() + shippingData.estimatedDays * 24 * 60 * 60 * 1000).toISOString()\n              : undefined,\n            notes: shippingData.notes,\n            createdAt: now,\n            updatedAt: now\n          };\n\n          // 更新發貨信息緩存\n          const { shippingInfos } = get();\n          const newShippingInfos = new Map(shippingInfos);\n          newShippingInfos.set(orderId, shippingInfo);\n\n          // 更新訂單狀態為已發貨\n          get().updateOrderStatus(orderId, OrderStatus.SHIPPED);\n\n          set({\n            shippingInfos: newShippingInfos,\n            isLoading: false\n          });\n\n          return shippingInfo;\n        } catch (error) {\n          set({\n            error: error instanceof Error ? error.message : '發貨失敗',\n            isLoading: false\n          });\n          throw error;\n        }\n      },\n\n      getOrderShipping: async (orderId: number) => {\n        const { shippingInfos } = get();\n\n        // 先檢查緩存\n        const cachedShipping = shippingInfos.get(orderId);\n        if (cachedShipping) {\n          return cachedShipping;\n        }\n\n        try {\n          // 實際應該調用後端API\n          // const response = await fetch(`/api/orders/${orderId}/shipping`);\n          // if (response.ok) {\n          //   const shippingInfo = await response.json();\n          //   // 更新緩存\n          //   const newShippingInfos = new Map(shippingInfos);\n          //   newShippingInfos.set(orderId, shippingInfo);\n          //   set({ shippingInfos: newShippingInfos });\n          //   return shippingInfo;\n          // }\n\n          return null;\n        } catch (error) {\n          console.error('獲取發貨信息失敗:', error);\n          return null;\n        }\n      },\n\n      updateOrderShipping: async (orderId: number, shippingData: ShippingRequest) => {\n        set({ isLoading: true, error: null });\n\n        try {\n          // 模擬API調用\n          await new Promise(resolve => setTimeout(resolve, 1000));\n\n          // 實際應該調用後端API\n          // const response = await fetch(`/api/orders/${orderId}/shipping`, {\n          //   method: 'PUT',\n          //   headers: { 'Content-Type': 'application/json' },\n          //   body: JSON.stringify(shippingData)\n          // });\n          // const shippingInfo = await response.json();\n\n          // 模擬更新發貨信息\n          const { shippingInfos } = get();\n          const existingShipping = shippingInfos.get(orderId);\n\n          if (!existingShipping) {\n            throw new Error('該訂單尚未發貨');\n          }\n\n          const updatedShipping: ShippingInfo = {\n            ...existingShipping,\n            trackingNumber: shippingData.trackingNumber,\n            carrier: shippingData.carrier,\n            shippingMethod: shippingData.shippingMethod,\n            notes: shippingData.notes,\n            estimatedDelivery: shippingData.estimatedDays\n              ? new Date(new Date(existingShipping.shippedAt).getTime() + shippingData.estimatedDays * 24 * 60 * 60 * 1000).toISOString()\n              : existingShipping.estimatedDelivery,\n            updatedAt: new Date().toISOString()\n          };\n\n          // 更新緩存\n          const newShippingInfos = new Map(shippingInfos);\n          newShippingInfos.set(orderId, updatedShipping);\n\n          set({\n            shippingInfos: newShippingInfos,\n            isLoading: false\n          });\n\n          return updatedShipping;\n        } catch (error) {\n          set({\n            error: error instanceof Error ? error.message : '更新發貨信息失敗',\n            isLoading: false\n          });\n          throw error;\n        }\n      },\n\n      getOrdersReadyToShip: () => {\n        const { orders } = get();\n        return orders.filter(order =>\n          order.status === OrderStatus.PAID || order.status === OrderStatus.PROCESSING\n        );\n      }\n    }),\n    {\n      name: 'order-storage',\n      version: 1,\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAgCA,QAAQ;AACR,MAAM,sBAAsB;IAC1B,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACvE,OAAO,CAAC,GAAG,EAAE,YAAY,QAAQ;AACnC;AAEA,uBAAuB;AACvB,IAAI,cAAc;AAGX,MAAM,gBAAgB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,QAAQ,EAAE;QACV,cAAc;QACd,WAAW;QACX,OAAO;QACP,eAAe,IAAI;QAEnB,OAAO;QACP,aAAa,OAAO;YAClB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,YAAY;gBACZ,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,eAAe;gBACf,oGAAoG;gBACpG,uCAAuC;gBAEvC,SAAS;gBACT,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,cAAc;gBAEpB,8BAA8B;gBAC9B,MAAM,aAA0B,UAAU,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC3D,WAAW,KAAK,SAAS;wBACzB,aAAa,CAAC,GAAG,EAAE,KAAK,SAAS,EAAE;wBACnC,cAAc;wBACd,OAAO;wBACP,UAAU,KAAK,QAAQ;wBACvB,UAAU,QAAQ,KAAK,QAAQ;oBACjC,CAAC;gBAED,MAAM,WAAW,WAAW,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;gBACvE,MAAM,cAAc,YAAY,KAAK,IAAI;gBACzC,MAAM,WAAW;gBACjB,MAAM,cAAc,WAAW,cAAc;gBAE7C,MAAM,WAAkB;oBACtB,IAAI;oBACJ;oBACA,QAAQ,qHAAA,CAAA,cAAW,CAAC,OAAO;oBAC3B,OAAO;oBACP;oBACA;oBACA;oBACA;oBACA,iBAAiB;wBACf,IAAI,UAAU,iBAAiB;wBAC/B,eAAe;wBACf,OAAO;wBACP,UAAU;wBACV,MAAM;wBACN,UAAU;wBACV,eAAe;wBACf,WAAW;oBACb;oBACA,eAAe,UAAU,aAAa;oBACtC,eAAe;oBACf,WAAW;oBACX,WAAW;oBACX,OAAO,UAAU,KAAK;gBACxB;gBAEA,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,IAAI;oBACF,QAAQ;wBAAC;2BAAa;qBAAO;oBAC7B,cAAc;oBACd,WAAW;gBACb;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,MAAM;YACR;QACF;QAEA,WAAW;QACX,cAAc,CAAC;YACb,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,OAAO;QAClD;QAEA,SAAS;QACT,eAAe,CAAC;YACd,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,iCAAiC;YACjC,OAAO;QACT;QAEA,SAAS;QACT,mBAAmB,CAAC,SAAiB;YACnC,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,MAAM,gBAAgB,OAAO,GAAG,CAAC,CAAA;gBAC/B,IAAI,MAAM,EAAE,KAAK,SAAS;oBACxB,MAAM,eAAe;wBACnB,GAAG,KAAK;wBACR;wBACA,WAAW,IAAI,OAAO,WAAW;oBACnC;oBAEA,sBAAsB;oBACtB,IAAI,WAAW,qHAAA,CAAA,cAAW,CAAC,IAAI,EAAE;wBAC/B,aAAa,aAAa,GAAG;wBAC7B,aAAa,MAAM,GAAG,IAAI,OAAO,WAAW;oBAC9C;oBAEA,OAAO;gBACT;gBACA,OAAO;YACT;YAEA,IAAI;gBAAE,QAAQ;YAAc;QAC9B;QAEA,OAAO;QACP,aAAa,CAAC;YACZ,MAAM,iBAAiB,CAAC,SAAS,qHAAA,CAAA,cAAW,CAAC,SAAS;QACxD;QAEA,SAAS;QACT,iBAAiB,CAAC;YAChB,IAAI;gBAAE,cAAc;YAAM;QAC5B;QAEA,OAAO;QACP,aAAa;YACX,IAAI;gBACF,QAAQ,EAAE;gBACV,cAAc;gBACd,OAAO;YACT;QACF;QAEA,WAAW;QACX,mBAAmB,CAAC;YAClB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QACjD;QAEA,QAAQ;QACR,eAAe;YACb,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,OACJ,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK,qHAAA,CAAA,cAAW,CAAC,SAAS,EACtD,MAAM,CAAC,CAAC,OAAO,QAAU,QAAQ,MAAM,WAAW,EAAE;QACzD;QAEA,SAAS;QACT,WAAW,OAAO,SAAiB;YACjC,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,UAAU;gBACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,cAAc;gBACd,gEAAgE;gBAChE,oBAAoB;gBACpB,qDAAqD;gBACrD,uCAAuC;gBACvC,MAAM;gBACN,8CAA8C;gBAE9C,WAAW;gBACX,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,eAA6B;oBACjC,IAAI,KAAK,GAAG;oBACZ;oBACA,gBAAgB,aAAa,cAAc;oBAC3C,SAAS,aAAa,OAAO;oBAC7B,gBAAgB,aAAa,cAAc;oBAC3C,WAAW;oBACX,mBAAmB,aAAa,aAAa,GACzC,IAAI,KAAK,KAAK,GAAG,KAAK,aAAa,aAAa,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW,KACnF;oBACJ,OAAO,aAAa,KAAK;oBACzB,WAAW;oBACX,WAAW;gBACb;gBAEA,WAAW;gBACX,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,mBAAmB,IAAI,IAAI;gBACjC,iBAAiB,GAAG,CAAC,SAAS;gBAE9B,aAAa;gBACb,MAAM,iBAAiB,CAAC,SAAS,qHAAA,CAAA,cAAW,CAAC,OAAO;gBAEpD,IAAI;oBACF,eAAe;oBACf,WAAW;gBACb;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,MAAM;YACR;QACF;QAEA,kBAAkB,OAAO;YACvB,MAAM,EAAE,aAAa,EAAE,GAAG;YAE1B,QAAQ;YACR,MAAM,iBAAiB,cAAc,GAAG,CAAC;YACzC,IAAI,gBAAgB;gBAClB,OAAO;YACT;YAEA,IAAI;gBACF,cAAc;gBACd,mEAAmE;gBACnE,qBAAqB;gBACrB,gDAAgD;gBAChD,YAAY;gBACZ,qDAAqD;gBACrD,iDAAiD;gBACjD,8CAA8C;gBAC9C,yBAAyB;gBACzB,IAAI;gBAEJ,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,OAAO;YACT;QACF;QAEA,qBAAqB,OAAO,SAAiB;YAC3C,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,UAAU;gBACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,cAAc;gBACd,oEAAoE;gBACpE,mBAAmB;gBACnB,qDAAqD;gBACrD,uCAAuC;gBACvC,MAAM;gBACN,8CAA8C;gBAE9C,WAAW;gBACX,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,MAAM,mBAAmB,cAAc,GAAG,CAAC;gBAE3C,IAAI,CAAC,kBAAkB;oBACrB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAgC;oBACpC,GAAG,gBAAgB;oBACnB,gBAAgB,aAAa,cAAc;oBAC3C,SAAS,aAAa,OAAO;oBAC7B,gBAAgB,aAAa,cAAc;oBAC3C,OAAO,aAAa,KAAK;oBACzB,mBAAmB,aAAa,aAAa,GACzC,IAAI,KAAK,IAAI,KAAK,iBAAiB,SAAS,EAAE,OAAO,KAAK,aAAa,aAAa,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW,KACvH,iBAAiB,iBAAiB;oBACtC,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAEA,OAAO;gBACP,MAAM,mBAAmB,IAAI,IAAI;gBACjC,iBAAiB,GAAG,CAAC,SAAS;gBAE9B,IAAI;oBACF,eAAe;oBACf,WAAW;gBACb;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;gBACA,MAAM;YACR;QACF;QAEA,sBAAsB;YACpB,MAAM,EAAE,MAAM,EAAE,GAAG;YACnB,OAAO,OAAO,MAAM,CAAC,CAAA,QACnB,MAAM,MAAM,KAAK,qHAAA,CAAA,cAAW,CAAC,IAAI,IAAI,MAAM,MAAM,KAAK,qHAAA,CAAA,cAAW,CAAC,UAAU;QAEhF;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS;AACX", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Checkout/AddressSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { PlusIcon, CheckIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';\nimport { useAddressStore } from '../../store/addressStore';\nimport { Address } from '../../types/order';\n\ninterface AddressSelectorProps {\n  onAddressSelect?: (address: Address) => void;\n}\n\nconst AddressSelector: React.FC<AddressSelectorProps> = ({ onAddressSelect }) => {\n  const {\n    addresses,\n    selectedAddressId,\n    selectAddress,\n    setDefaultAddress,\n    removeAddress\n  } = useAddressStore();\n  \n  const [showAddForm, setShowAddForm] = useState(false);\n  const [editingAddress, setEditingAddress] = useState<Address | null>(null);\n\n  const handleSelectAddress = (address: Address) => {\n    selectAddress(address.id!);\n    onAddressSelect?.(address);\n  };\n\n  const handleSetDefault = (addressId: number) => {\n    setDefaultAddress(addressId);\n  };\n\n  const handleDelete = (addressId: number) => {\n    if (confirm('確定要刪除這個地址嗎？')) {\n      removeAddress(addressId);\n    }\n  };\n\n  const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);\n\n  return (\n    <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">收貨地址</h3>\n        <button\n          onClick={() => setShowAddForm(true)}\n          className=\"flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium\"\n        >\n          <PlusIcon className=\"w-4 h-4 mr-1\" />\n          新增地址\n        </button>\n      </div>\n\n      {addresses.length === 0 ? (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500 mb-4\">您還沒有收貨地址</p>\n          <button\n            onClick={() => setShowAddForm(true)}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700\"\n          >\n            添加收貨地址\n          </button>\n        </div>\n      ) : (\n        <div className=\"space-y-3\">\n          {addresses.map((address) => (\n            <div\n              key={address.id}\n              className={`border rounded-lg p-4 cursor-pointer transition-colors ${\n                selectedAddressId === address.id\n                  ? 'border-blue-500 bg-blue-50'\n                  : 'border-gray-200 hover:border-gray-300'\n              }`}\n              onClick={() => handleSelectAddress(address)}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <span className=\"font-medium text-gray-900\">\n                      {address.recipientName}\n                    </span>\n                    <span className=\"text-gray-600\">{address.phone}</span>\n                    {address.isDefault && (\n                      <span className=\"bg-red-100 text-red-600 text-xs px-2 py-1 rounded\">\n                        默認\n                      </span>\n                    )}\n                  </div>\n                  <p className=\"text-gray-600 text-sm\">\n                    {address.province} {address.city} {address.district} {address.detailAddress}\n                  </p>\n                  {address.postalCode && (\n                    <p className=\"text-gray-500 text-xs mt-1\">\n                      郵編: {address.postalCode}\n                    </p>\n                  )}\n                </div>\n                \n                <div className=\"flex items-center space-x-2 ml-4\">\n                  {selectedAddressId === address.id && (\n                    <CheckIcon className=\"w-5 h-5 text-blue-600\" />\n                  )}\n                  \n                  <div className=\"flex space-x-1\">\n                    {!address.isDefault && (\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleSetDefault(address.id!);\n                        }}\n                        className=\"text-gray-400 hover:text-blue-600 text-xs\"\n                      >\n                        設為默認\n                      </button>\n                    )}\n                    \n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        setEditingAddress(address);\n                      }}\n                      className=\"text-gray-400 hover:text-blue-600\"\n                    >\n                      <PencilIcon className=\"w-4 h-4\" />\n                    </button>\n                    \n                    <button\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleDelete(address.id!);\n                      }}\n                      className=\"text-gray-400 hover:text-red-600\"\n                    >\n                      <TrashIcon className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* 這裡可以添加地址表單組件 */}\n      {showAddForm && (\n        <div className=\"mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50\">\n          <p className=\"text-gray-600\">地址表單組件將在下一步實現</p>\n          <button\n            onClick={() => setShowAddForm(false)}\n            className=\"mt-2 text-blue-600 hover:text-blue-800 text-sm\"\n          >\n            取消\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AddressSelector;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAWA,MAAM,kBAAkD,CAAC,EAAE,eAAe,EAAE;IAC1E,MAAM,EACJ,SAAS,EACT,iBAAiB,EACjB,aAAa,EACb,iBAAiB,EACjB,aAAa,EACd,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,MAAM,sBAAsB,CAAC;QAC3B,cAAc,QAAQ,EAAE;QACxB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;IACpB;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,gBAAgB;YAC1B,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAE3D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,8OAAC,+MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAKxC,UAAU,MAAM,KAAK,kBACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;kCACX;;;;;;;;;;;qCAKH,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,wBACd,8OAAC;wBAEC,WAAW,CAAC,uDAAuD,EACjE,sBAAsB,QAAQ,EAAE,GAC5B,+BACA,yCACJ;wBACF,SAAS,IAAM,oBAAoB;kCAEnC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,QAAQ,aAAa;;;;;;8DAExB,8OAAC;oDAAK,WAAU;8DAAiB,QAAQ,KAAK;;;;;;gDAC7C,QAAQ,SAAS,kBAChB,8OAAC;oDAAK,WAAU;8DAAoD;;;;;;;;;;;;sDAKxE,8OAAC;4CAAE,WAAU;;gDACV,QAAQ,QAAQ;gDAAC;gDAAE,QAAQ,IAAI;gDAAC;gDAAE,QAAQ,QAAQ;gDAAC;gDAAE,QAAQ,aAAa;;;;;;;wCAE5E,QAAQ,UAAU,kBACjB,8OAAC;4CAAE,WAAU;;gDAA6B;gDACnC,QAAQ,UAAU;;;;;;;;;;;;;8CAK7B,8OAAC;oCAAI,WAAU;;wCACZ,sBAAsB,QAAQ,EAAE,kBAC/B,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDAGvB,8OAAC;4CAAI,WAAU;;gDACZ,CAAC,QAAQ,SAAS,kBACjB,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,iBAAiB,QAAQ,EAAE;oDAC7B;oDACA,WAAU;8DACX;;;;;;8DAKH,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,kBAAkB;oDACpB;oDACA,WAAU;8DAEV,cAAA,8OAAC,mNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAGxB,8OAAC;oDACC,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,aAAa,QAAQ,EAAE;oDACzB;oDACA,WAAU;8DAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAlExB,QAAQ,EAAE;;;;;;;;;;YA6EtB,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Checkout/PaymentMethodSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { CheckIcon } from '@heroicons/react/24/outline';\nimport { PaymentMethod } from '../../types/order';\n\ninterface PaymentMethodSelectorProps {\n  selectedMethod: PaymentMethod | null;\n  onMethodSelect: (method: PaymentMethod) => void;\n}\n\n// 支付方式配置\nconst paymentMethods = [\n  {\n    id: PaymentMethod.CREDIT_CARD,\n    name: '信用卡',\n    description: '支持 Visa、MasterCard、銀聯等',\n    icon: '💳',\n    available: true\n  },\n  {\n    id: PaymentMethod.DEBIT_CARD,\n    name: '借記卡',\n    description: '支持各大銀行借記卡',\n    icon: '🏦',\n    available: true\n  },\n  {\n    id: PaymentMethod.ALIPAY,\n    name: '支付寶',\n    description: '使用支付寶掃碼支付',\n    icon: '🅰️',\n    available: true\n  },\n  {\n    id: PaymentMethod.WECHAT_PAY,\n    name: '微信支付',\n    description: '使用微信掃碼支付',\n    icon: '💬',\n    available: true\n  },\n  {\n    id: PaymentMethod.PAYPAL,\n    name: 'PayPal',\n    description: '國際支付平台',\n    icon: '🅿️',\n    available: false // 暫不可用\n  },\n  {\n    id: PaymentMethod.CASH_ON_DELIVERY,\n    name: '貨到付款',\n    description: '送貨時現金支付',\n    icon: '💰',\n    available: true\n  }\n];\n\nconst PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({\n  selectedMethod,\n  onMethodSelect\n}) => {\n  return (\n    <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">支付方式</h3>\n      \n      <div className=\"space-y-3\">\n        {paymentMethods.map((method) => (\n          <div\n            key={method.id}\n            className={`border rounded-lg p-4 cursor-pointer transition-colors ${\n              !method.available\n                ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-60'\n                : selectedMethod === method.id\n                ? 'border-blue-500 bg-blue-50'\n                : 'border-gray-200 hover:border-gray-300'\n            }`}\n            onClick={() => method.available && onMethodSelect(method.id)}\n          >\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-3\">\n                <span className=\"text-2xl\">{method.icon}</span>\n                <div>\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"font-medium text-gray-900\">\n                      {method.name}\n                    </span>\n                    {!method.available && (\n                      <span className=\"bg-gray-200 text-gray-600 text-xs px-2 py-1 rounded\">\n                        暫不可用\n                      </span>\n                    )}\n                  </div>\n                  <p className=\"text-gray-600 text-sm\">{method.description}</p>\n                </div>\n              </div>\n              \n              {selectedMethod === method.id && method.available && (\n                <CheckIcon className=\"w-5 h-5 text-blue-600\" />\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n      \n      {/* 支付安全提示 */}\n      <div className=\"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg\">\n        <div className=\"flex items-start space-x-2\">\n          <span className=\"text-green-600 text-lg\">🔒</span>\n          <div>\n            <h4 className=\"text-green-800 font-medium text-sm\">安全支付保障</h4>\n            <p className=\"text-green-700 text-xs mt-1\">\n              我們使用SSL加密技術保護您的支付信息，確保交易安全可靠\n            </p>\n          </div>\n        </div>\n      </div>\n      \n      {/* 支付說明 */}\n      {selectedMethod && (\n        <div className=\"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n          <h4 className=\"text-blue-800 font-medium text-sm mb-2\">支付說明</h4>\n          <div className=\"text-blue-700 text-xs space-y-1\">\n            {selectedMethod === PaymentMethod.CREDIT_CARD && (\n              <p>• 支付完成後，我們會立即處理您的訂單</p>\n            )}\n            {selectedMethod === PaymentMethod.ALIPAY && (\n              <p>• 點擊確認訂單後，將跳轉到支付寶支付頁面</p>\n            )}\n            {selectedMethod === PaymentMethod.WECHAT_PAY && (\n              <p>• 點擊確認訂單後，將顯示微信支付二維碼</p>\n            )}\n            {selectedMethod === PaymentMethod.CASH_ON_DELIVERY && (\n              <>\n                <p>• 送貨員會在送達時收取現金</p>\n                <p>• 請準備好零錢，送貨員可能無法找零</p>\n              </>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PaymentMethodSelector;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWA,SAAS;AACT,MAAM,iBAAiB;IACrB;QACE,IAAI,qHAAA,CAAA,gBAAa,CAAC,WAAW;QAC7B,MAAM;QACN,aAAa;QACb,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI,qHAAA,CAAA,gBAAa,CAAC,UAAU;QAC5B,MAAM;QACN,aAAa;QACb,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI,qHAAA,CAAA,gBAAa,CAAC,MAAM;QACxB,MAAM;QACN,aAAa;QACb,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI,qHAAA,CAAA,gBAAa,CAAC,UAAU;QAC5B,MAAM;QACN,aAAa;QACb,MAAM;QACN,WAAW;IACb;IACA;QACE,IAAI,qHAAA,CAAA,gBAAa,CAAC,MAAM;QACxB,MAAM;QACN,aAAa;QACb,MAAM;QACN,WAAW,MAAM,OAAO;IAC1B;IACA;QACE,IAAI,qHAAA,CAAA,gBAAa,CAAC,gBAAgB;QAClC,MAAM;QACN,aAAa;QACb,MAAM;QACN,WAAW;IACb;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,cAAc,EACd,cAAc,EACf;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA2C;;;;;;0BAEzD,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC;wBAEC,WAAW,CAAC,uDAAuD,EACjE,CAAC,OAAO,SAAS,GACb,6DACA,mBAAmB,OAAO,EAAE,GAC5B,+BACA,yCACJ;wBACF,SAAS,IAAM,OAAO,SAAS,IAAI,eAAe,OAAO,EAAE;kCAE3D,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAY,OAAO,IAAI;;;;;;sDACvC,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,OAAO,IAAI;;;;;;wDAEb,CAAC,OAAO,SAAS,kBAChB,8OAAC;4DAAK,WAAU;sEAAsD;;;;;;;;;;;;8DAK1E,8OAAC;oDAAE,WAAU;8DAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;gCAI3D,mBAAmB,OAAO,EAAE,IAAI,OAAO,SAAS,kBAC/C,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;uBA7BpB,OAAO,EAAE;;;;;;;;;;0BAqCpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAyB;;;;;;sCACzC,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;YAQhD,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAI,WAAU;;4BACZ,mBAAmB,qHAAA,CAAA,gBAAa,CAAC,WAAW,kBAC3C,8OAAC;0CAAE;;;;;;4BAEJ,mBAAmB,qHAAA,CAAA,gBAAa,CAAC,MAAM,kBACtC,8OAAC;0CAAE;;;;;;4BAEJ,mBAAmB,qHAAA,CAAA,gBAAa,CAAC,UAAU,kBAC1C,8OAAC;0CAAE;;;;;;4BAEJ,mBAAmB,qHAAA,CAAA,gBAAa,CAAC,gBAAgB,kBAChD;;kDACE,8OAAC;kDAAE;;;;;;kDACH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;uCAEe", "debugId": null}}, {"offset": {"line": 1050, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Checkout/OrderSummary.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport { useCartStore } from '../../store/cartStore';\n\ninterface OrderSummaryProps {\n  showItems?: boolean;\n  className?: string;\n}\n\nconst OrderSummary: React.FC<OrderSummaryProps> = ({ \n  showItems = true, \n  className = '' \n}) => {\n  const { items, totalItems, totalPrice } = useCartStore();\n\n  // 計算運費\n  const shippingFee = totalPrice >= 99 ? 0 : 10;\n  const finalTotal = totalPrice + shippingFee;\n\n  // 處理圖片URL\n  const getImageUrl = (url: string, productName: string) => {\n    if (!url) return '/placeholder.jpg';\n    \n    if (url.startsWith('/images/')) {\n      return `https://via.placeholder.com/80x80/f0f0f0/666666?text=${encodeURIComponent(productName)}`;\n    }\n    \n    try {\n      new URL(url);\n      return url;\n    } catch {\n      return `https://via.placeholder.com/80x80/f0f0f0/666666?text=${encodeURIComponent(productName)}`;\n    }\n  };\n\n  return (\n    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">訂單摘要</h3>\n      \n      {/* 商品列表 */}\n      {showItems && items.length > 0 && (\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-gray-700 mb-3\">\n            商品清單 ({totalItems} 件)\n          </h4>\n          <div className=\"space-y-3 max-h-60 overflow-y-auto\">\n            {items.map((item) => (\n              <div key={item.id} className=\"flex items-center space-x-3\">\n                <div className=\"relative w-12 h-12 flex-shrink-0\">\n                  <Image\n                    src={getImageUrl(item.imageUrl, item.name)}\n                    alt={item.name}\n                    fill\n                    style={{ objectFit: \"cover\" }}\n                    className=\"rounded-md\"\n                  />\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <p className=\"text-sm font-medium text-gray-900 line-clamp-1\">\n                    {item.name}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    ¥{item.price.toFixed(2)} × {item.quantity}\n                  </p>\n                </div>\n                <div className=\"text-sm font-medium text-gray-900\">\n                  ¥{(item.price * item.quantity).toFixed(2)}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n      \n      {/* 價格明細 */}\n      <div className=\"space-y-3 border-t border-gray-200 pt-4\">\n        <div className=\"flex justify-between text-gray-600\">\n          <span>商品小計</span>\n          <span>¥{totalPrice.toFixed(2)}</span>\n        </div>\n        \n        <div className=\"flex justify-between text-gray-600\">\n          <span>運費</span>\n          <span className={shippingFee === 0 ? 'text-green-600' : ''}>\n            {shippingFee === 0 ? '免費' : `¥${shippingFee.toFixed(2)}`}\n          </span>\n        </div>\n        \n        {/* 免運費提示 */}\n        {shippingFee > 0 && (\n          <div className=\"text-xs text-orange-600 bg-orange-50 p-2 rounded\">\n            再購買 ¥{(99 - totalPrice).toFixed(2)} 即可享受免運費\n          </div>\n        )}\n        \n        {shippingFee === 0 && totalPrice >= 99 && (\n          <div className=\"text-xs text-green-600 bg-green-50 p-2 rounded\">\n            🎉 恭喜！您已享受免運費優惠\n          </div>\n        )}\n        \n        {/* 優惠券 */}\n        <div className=\"flex justify-between text-gray-600\">\n          <span>優惠券</span>\n          <span className=\"text-green-600\">-¥0.00</span>\n        </div>\n        \n        {/* 總計 */}\n        <div className=\"flex justify-between text-lg font-semibold text-gray-900 border-t border-gray-200 pt-3\">\n          <span>總計</span>\n          <span className=\"text-red-600\">¥{finalTotal.toFixed(2)}</span>\n        </div>\n      </div>\n      \n      {/* 優惠信息 */}\n      <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n        <div className=\"flex items-start space-x-2\">\n          <span className=\"text-blue-600 text-sm\">💡</span>\n          <div className=\"text-blue-700 text-xs\">\n            <p className=\"font-medium\">購物提示</p>\n            <ul className=\"mt-1 space-y-1\">\n              <li>• 滿99元免運費</li>\n              <li>• 支持7天無理由退換貨</li>\n              <li>• 正品保證，假一賠十</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n      \n      {/* 空購物車提示 */}\n      {items.length === 0 && (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500 mb-2\">購物車為空</p>\n          <p className=\"text-sm text-gray-400\">\n            請先添加商品到購物車\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default OrderSummary;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAWA,MAAM,eAA4C,CAAC,EACjD,YAAY,IAAI,EAChB,YAAY,EAAE,EACf;IACC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAErD,OAAO;IACP,MAAM,cAAc,cAAc,KAAK,IAAI;IAC3C,MAAM,aAAa,aAAa;IAEhC,UAAU;IACV,MAAM,cAAc,CAAC,KAAa;QAChC,IAAI,CAAC,KAAK,OAAO;QAEjB,IAAI,IAAI,UAAU,CAAC,aAAa;YAC9B,OAAO,CAAC,qDAAqD,EAAE,mBAAmB,cAAc;QAClG;QAEA,IAAI;YACF,IAAI,IAAI;YACR,OAAO;QACT,EAAE,OAAM;YACN,OAAO,CAAC,qDAAqD,EAAE,mBAAmB,cAAc;QAClG;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;;0BAC3E,8OAAC;gBAAG,WAAU;0BAA2C;;;;;;YAGxD,aAAa,MAAM,MAAM,GAAG,mBAC3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAyC;4BAC9C;4BAAW;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gCAAkB,WAAU;;kDAC3B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,YAAY,KAAK,QAAQ,EAAE,KAAK,IAAI;4CACzC,KAAK,KAAK,IAAI;4CACd,IAAI;4CACJ,OAAO;gDAAE,WAAW;4CAAQ;4CAC5B,WAAU;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAE,WAAU;;oDAAwB;oDACjC,KAAK,KAAK,CAAC,OAAO,CAAC;oDAAG;oDAAI,KAAK,QAAQ;;;;;;;;;;;;;kDAG7C,8OAAC;wCAAI,WAAU;;4CAAoC;4CAC/C,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC;;;;;;;;+BAnBjC,KAAK,EAAE;;;;;;;;;;;;;;;;0BA4BzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;;oCAAK;oCAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;kCAG7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAW,gBAAgB,IAAI,mBAAmB;0CACrD,gBAAgB,IAAI,OAAO,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,IAAI;;;;;;;;;;;;oBAK3D,cAAc,mBACb,8OAAC;wBAAI,WAAU;;4BAAmD;4BAC1D,CAAC,KAAK,UAAU,EAAE,OAAO,CAAC;4BAAG;;;;;;;oBAItC,gBAAgB,KAAK,cAAc,oBAClC,8OAAC;wBAAI,WAAU;kCAAiD;;;;;;kCAMlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;;;;;;;kCAInC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAK,WAAU;;oCAAe;oCAAE,WAAW,OAAO,CAAC;;;;;;;;;;;;;;;;;;;0BAKxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;sCACxC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAc;;;;;;8CAC3B,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOX,MAAM,MAAM,KAAK,mBAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAO/C;uCAEe", "debugId": null}}, {"offset": {"line": 1427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/app/checkout/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { ArrowLeftIcon } from '@heroicons/react/24/outline';\nimport { useCartStore } from '../../store/cartStore';\nimport { useAddressStore } from '../../store/addressStore';\nimport { useOrderStore } from '../../store/orderStore';\nimport { PaymentMethod } from '../../types/order';\nimport AddressSelector from '../../components/Checkout/AddressSelector';\nimport PaymentMethodSelector from '../../components/Checkout/PaymentMethodSelector';\nimport OrderSummary from '../../components/Checkout/OrderSummary';\nimport toast from 'react-hot-toast';\n\nexport default function CheckoutPage() {\n  const router = useRouter();\n  const { items, totalPrice, clearCart } = useCartStore();\n  const { getSelectedAddress } = useAddressStore();\n  const { createOrder } = useOrderStore();\n  \n  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);\n  const [orderNotes, setOrderNotes] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [agreedToTerms, setAgreedToTerms] = useState(false);\n\n  // 檢查購物車是否為空\n  useEffect(() => {\n    if (items.length === 0) {\n      toast.error('購物車為空，請先添加商品');\n      router.push('/cart');\n    }\n  }, [items, router]);\n\n  // 添加一些默認地址（模擬數據）\n  useEffect(() => {\n    const { addresses, addAddress } = useAddressStore.getState();\n    if (addresses.length === 0) {\n      // 添加默認地址\n      addAddress({\n        recipientName: '張三',\n        phone: '13800138000',\n        province: '廣東省',\n        city: '深圳市',\n        district: '南山區',\n        detailAddress: '科技園南區深南大道10000號',\n        postalCode: '518000',\n        isDefault: true\n      });\n    }\n  }, []);\n\n  const selectedAddress = getSelectedAddress();\n  const shippingFee = totalPrice >= 99 ? 0 : 10;\n  const finalTotal = totalPrice + shippingFee;\n\n  const handleSubmitOrder = async () => {\n    // 驗證必填項\n    if (!selectedAddress) {\n      toast.error('請選擇收貨地址');\n      return;\n    }\n\n    if (!selectedPaymentMethod) {\n      toast.error('請選擇支付方式');\n      return;\n    }\n\n    if (!agreedToTerms) {\n      toast.error('請同意服務條款和隱私政策');\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    try {\n      // 創建訂單\n      const orderData = {\n        items: items.map(item => ({\n          productId: item.id,\n          quantity: item.quantity\n        })),\n        shippingAddressId: selectedAddress.id!,\n        paymentMethod: selectedPaymentMethod,\n        notes: orderNotes.trim() || undefined\n      };\n\n      const order = await createOrder(orderData);\n      \n      // 清空購物車\n      clearCart();\n      \n      // 顯示成功消息\n      toast.success('訂單創建成功！');\n      \n      // 跳轉到訂單確認頁面\n      router.push(`/orders/${order.id}`);\n      \n    } catch (error) {\n      toast.error('創建訂單失敗，請重試');\n      console.error('創建訂單失敗:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (items.length === 0) {\n    return null; // 避免閃爍，useEffect會處理重定向\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* 頁面標題 */}\n      <div className=\"flex items-center mb-6\">\n        <Link\n          href=\"/cart\"\n          className=\"flex items-center text-gray-600 hover:text-gray-800 mr-4\"\n        >\n          <ArrowLeftIcon className=\"w-5 h-5 mr-1\" />\n          返回購物車\n        </Link>\n        <h1 className=\"text-2xl font-bold text-gray-900\">結算</h1>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* 左側：地址和支付方式 */}\n        <div className=\"lg:col-span-2 space-y-6\">\n          {/* 收貨地址 */}\n          <AddressSelector />\n          \n          {/* 支付方式 */}\n          <PaymentMethodSelector\n            selectedMethod={selectedPaymentMethod}\n            onMethodSelect={setSelectedPaymentMethod}\n          />\n          \n          {/* 訂單備註 */}\n          <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">訂單備註</h3>\n            <textarea\n              value={orderNotes}\n              onChange={(e) => setOrderNotes(e.target.value)}\n              placeholder=\"如有特殊要求，請在此說明（選填）\"\n              className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              rows={3}\n              maxLength={200}\n            />\n            <div className=\"text-right text-xs text-gray-500 mt-1\">\n              {orderNotes.length}/200\n            </div>\n          </div>\n        </div>\n\n        {/* 右側：訂單摘要 */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"sticky top-4 space-y-6\">\n            <OrderSummary />\n            \n            {/* 服務條款 */}\n            <div className=\"bg-white rounded-lg border border-gray-200 p-6\">\n              <label className=\"flex items-start space-x-3\">\n                <input\n                  type=\"checkbox\"\n                  checked={agreedToTerms}\n                  onChange={(e) => setAgreedToTerms(e.target.checked)}\n                  className=\"mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <span className=\"text-sm text-gray-600\">\n                  我已閱讀並同意\n                  <Link href=\"/terms\" className=\"text-blue-600 hover:text-blue-800 mx-1\">\n                    服務條款\n                  </Link>\n                  和\n                  <Link href=\"/privacy\" className=\"text-blue-600 hover:text-blue-800 mx-1\">\n                    隱私政策\n                  </Link>\n                </span>\n              </label>\n            </div>\n            \n            {/* 提交訂單按鈕 */}\n            <button\n              onClick={handleSubmitOrder}\n              disabled={isSubmitting || !selectedAddress || !selectedPaymentMethod || !agreedToTerms}\n              className={`w-full py-3 px-4 rounded-lg font-semibold transition-colors ${\n                isSubmitting || !selectedAddress || !selectedPaymentMethod || !agreedToTerms\n                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                  : 'bg-red-600 text-white hover:bg-red-700'\n              }`}\n            >\n              {isSubmitting ? '提交中...' : `確認訂單 ¥${finalTotal.toFixed(2)}`}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IACpD,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAC7C,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD;IAEpC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,YAAY;IACZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAO;KAAO;IAElB,iBAAiB;IACjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,4HAAA,CAAA,kBAAe,CAAC,QAAQ;QAC1D,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,SAAS;YACT,WAAW;gBACT,eAAe;gBACf,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,UAAU;gBACV,eAAe;gBACf,YAAY;gBACZ,WAAW;YACb;QACF;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;IACxB,MAAM,cAAc,cAAc,KAAK,IAAI;IAC3C,MAAM,aAAa,aAAa;IAEhC,MAAM,oBAAoB;QACxB,QAAQ;QACR,IAAI,CAAC,iBAAiB;YACpB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,uBAAuB;YAC1B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,eAAe;YAClB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,OAAO;YACP,MAAM,YAAY;gBAChB,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACxB,WAAW,KAAK,EAAE;wBAClB,UAAU,KAAK,QAAQ;oBACzB,CAAC;gBACD,mBAAmB,gBAAgB,EAAE;gBACrC,eAAe;gBACf,OAAO,WAAW,IAAI,MAAM;YAC9B;YAEA,MAAM,QAAQ,MAAM,YAAY;YAEhC,QAAQ;YACR;YAEA,SAAS;YACT,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAEd,YAAY;YACZ,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;QAEnC,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,OAAO,MAAM,uBAAuB;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,yNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAG5C,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;0BAGnD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,iJAAA,CAAA,UAAe;;;;;0CAGhB,8OAAC,uJAAA,CAAA,UAAqB;gCACpB,gBAAgB;gCAChB,gBAAgB;;;;;;0CAIlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;wCACV,MAAM;wCACN,WAAW;;;;;;kDAEb,8OAAC;wCAAI,WAAU;;4CACZ,WAAW,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,8IAAA,CAAA,UAAY;;;;;8CAGb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,OAAO;gDAClD,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;;oDAAwB;kEAEtC,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAyC;;;;;;oDAEhE;kEAEP,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAyC;;;;;;;;;;;;;;;;;;;;;;;8CAQ/E,8OAAC;oCACC,SAAS;oCACT,UAAU,gBAAgB,CAAC,mBAAmB,CAAC,yBAAyB,CAAC;oCACzE,WAAW,CAAC,4DAA4D,EACtE,gBAAgB,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,gBAC3D,iDACA,0CACJ;8CAED,eAAe,WAAW,CAAC,MAAM,EAAE,WAAW,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzE", "debugId": null}}]}