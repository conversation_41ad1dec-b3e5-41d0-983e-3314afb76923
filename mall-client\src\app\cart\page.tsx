'use client';

import React from 'react';
import Link from 'next/link';
import { ShoppingCartIcon } from '@heroicons/react/24/outline';
import { useCartStore } from '../../store/cartStore';
import CartItem from '../../components/Cart/CartItem';
import CartSummary from '../../components/Cart/CartSummary';

export default function CartPage() {
  const { items, totalItems, clearCart } = useCartStore();

  const handleCheckout = () => {
    // 導航到結算頁面
    window.location.href = '/checkout';
  };

  const handleClearCart = () => {
    if (confirm('確定要清空購物車嗎？此操作無法撤銷。')) {
      clearCart();
    }
  };

  // 空購物車狀態
  if (items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <ShoppingCartIcon className="w-12 h-12 text-gray-400" />
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">購物車是空的</h1>
          <p className="text-gray-600 mb-8">
            還沒有添加任何商品到購物車，快去挑選您喜歡的商品吧！
          </p>
          
          <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <Link
              href="/"
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
            >
              瀏覽商品
            </Link>
            <Link
              href="/products"
              className="inline-block border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
            >
              查看所有商品
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 頁面標題 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">購物車</h1>
          <p className="text-gray-600 mt-1">
            共 {totalItems} 件商品
          </p>
        </div>
        
        {/* 清空購物車按鈕 */}
        <button
          onClick={handleClearCart}
          className="text-red-600 hover:text-red-800 text-sm font-medium"
        >
          清空購物車
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 購物車商品列表 */}
        <div className="lg:col-span-2">
          <div className="space-y-4">
            {items.map((item) => (
              <CartItem key={item.id} item={item} />
            ))}
          </div>
          
          {/* 繼續購物提示 */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <ShoppingCartIcon className="w-5 h-5 text-blue-600" />
              <span className="text-blue-800 font-medium">想要更多商品？</span>
            </div>
            <p className="text-blue-700 text-sm mt-1">
              繼續瀏覽我們的商品，發現更多優質選擇
            </p>
            <div className="mt-3 space-x-3">
              <Link
                href="/"
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                返回首頁
              </Link>
              <Link
                href="/products"
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                瀏覽所有商品
              </Link>
            </div>
          </div>
        </div>

        {/* 購物車總計 */}
        <div className="lg:col-span-1">
          <div className="sticky top-4">
            <CartSummary onCheckout={handleCheckout} />
          </div>
        </div>
      </div>

      {/* 移動端固定底部結算按鈕 */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-10">
        <button
          onClick={handleCheckout}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
        >
          前往結算 (¥{useCartStore.getState().totalPrice.toFixed(2)})
        </button>
      </div>

      {/* 為移動端固定按鈕留出空間 */}
      <div className="lg:hidden h-20"></div>
    </div>
  );
}
